# Reusable React Components

This directory contains modern, responsive React components built with TypeScript, Tailwind CSS, and Framer Motion for a professional portfolio website.

## Component Architecture

The components are organized into two main categories:
- **General Components**: Reusable across the entire application
- **Home Section Components**: Specific sections for the homepage

## General Components

### 1. Navbar Component

A fully responsive navigation bar with smooth animations and accessibility features.

#### Features:
- **Responsive Design**: Adapts to all screen sizes with hamburger menu on mobile
- **Smooth Animations**: Framer Motion powered micro-animations
- **Accessibility**: Full ARIA support and keyboard navigation
- **Active Link Highlighting**: Shows current page with visual indicators using `aria-current`
- **Scroll Effects**: Changes appearance on scroll
- **Modern Design**: Gradient logo, soft shadows, and hover effects

#### Usage:
```tsx
import { Navbar } from './components';

function App() {
  return (
    <>
      <Navbar />
      {/* Your page content */}
    </>
  );
}
```

### 2. Footer Component

A comprehensive footer with multiple sections and social links.

#### Features:
- **Multi-Section Layout**: About, Quick Links, Projects, and Social sections
- **Responsive Grid**: Adapts layout based on screen size
- **Social Media Integration**: Configurable social links with hover effects
- **Back to Top Button**: Smooth scroll to top functionality
- **Contact Information**: Display contact details with icons
- **Consistent Styling**: Matches navbar design language

#### Usage:
```tsx
import { Footer } from './components';

function Page() {
  return (
    <div>
      {/* Your page content */}
      <Footer />
    </div>
  );
}
```

### 3. Breadcrumb Component

A semantic breadcrumb navigation component for improved UX and SEO.

#### Features:
- **Accessible Navigation**: Uses proper ARIA attributes
- **SEO Optimized**: Semantic HTML structure
- **Route Integration**: Works with React Router
- **Current Page Indication**: Uses `aria-current="page"`
- **Responsive Design**: Adapts to mobile screens

#### Usage:
```tsx
import { Breadcrumb } from './components';

function ProjectPage() {
  return (
    <div>
      <Breadcrumb />
      {/* Page content */}
    </div>
  );
}
```

### 4. ProjectCard Component

A versatile card component for displaying project information with multiple view modes.

#### Features:
- **Semantic HTML**: Uses `<article>` for better SEO
- **Multiple View Modes**: Grid and list layout options
- **Accessibility**: Full keyboard navigation and ARIA support
- **Responsive Images**: Optimized loading with lazy loading
- **Hover Animations**: Smooth Framer Motion effects
- **Tag System**: Technology tags with gradient styling
- **External Links**: GitHub and live demo link support

#### Usage:
```tsx
import { ProjectCard } from './components';

const project = {
  id: "1",
  title: "My Project",
  description: "Project description",
  tags: ["React", "TypeScript"],
  image: "/project-image.jpg",
  href: "/projects/my-project"
};

function ProjectsPage() {
  return (
    <ProjectCard 
      project={project} 
      viewMode="grid" 
      showLinks={true} 
    />
  );
}
```

### 5. ProjectGrid Component

A grid container for displaying multiple ProjectCard components.

#### Features:
- **Flexible Layout**: Supports both grid and list view modes
- **Responsive Grid**: Adapts columns based on screen size
- **Animation Staggering**: Coordinated entry animations
- **Empty State Handling**: Graceful handling of no projects
- **Search Integration**: Works with filtered project arrays

#### Usage:
```tsx
import { ProjectGrid } from './components';

function ProjectsPage() {
  return (
    <ProjectGrid 
      projects={filteredProjects}
      viewMode="grid"
      showLinks={true}
      className="custom-styling"
    />
  );
}
```

### 6. ThemeToggle Component

A theme switcher with both full and compact variants for light/dark mode.

#### Features:
- **Context Integration**: Uses React Context for theme state
- **Smooth Transitions**: Animated theme switching
- **Persistent State**: Remembers user preference
- **Accessibility**: Proper ARIA labels and keyboard support
- **Multiple Variants**: Full button and compact icon versions
- **System Preference**: Respects user's OS theme preference

#### Usage:
```tsx
import { ThemeToggle, CompactThemeToggle } from './components';

function Header() {
  return (
    <div>
      <ThemeToggle /> {/* Full button version */}
      <CompactThemeToggle /> {/* Icon only version */}
    </div>
  );
}
```

### 7. ScrollToTop Component

A floating button for smooth scrolling back to the top of the page.

#### Features:
- **Auto Hide/Show**: Appears only when user scrolls down
- **Smooth Animation**: Framer Motion powered entrance/exit
- **Accessibility**: Keyboard accessible with proper labels
- **Fixed Positioning**: Stays in consistent location
- **Mobile Optimized**: Appropriate sizing for touch devices

#### Usage:
```tsx
import { ScrollToTop } from './components';

function App() {
  return (
    <div>
      {/* Your app content */}
      <ScrollToTop />
    </div>
  );
}
```

## Home Section Components

### 1. HeroSection Component

The main landing section with animated introduction and call-to-action.

#### Features:
- **Gradient Background**: Blue to purple gradient with dark mode support
- **Typography Animation**: Staggered text animations
- **Call-to-Action Buttons**: Prominent action buttons with hover effects
- **Professional Copy**: Engaging introduction text
- **Responsive Design**: Optimized for all screen sizes

### 2. AboutSection Component

A detailed about section with professional summary and personal touch.

#### Features:
- **Two-Column Layout**: Text and visual content side by side
- **Gradient Headings**: Consistent blue-purple gradient styling
- **Responsive Images**: Optimized image handling
- **Professional Tone**: Well-crafted copy about experience and passion
- **Animation Effects**: Smooth entry animations

### 3. SkillsSection Component

A comprehensive skills showcase with categorized technical abilities.

#### Features:
- **Categorized Skills**: Organized by technology areas
- **Icon Integration**: Lucide React icons for visual appeal
- **Gradient Styling**: Consistent visual language
- **Card Layout**: Individual cards for each skill category
- **Hover Effects**: Interactive card animations
- **Responsive Grid**: Adapts from 1 to 3 columns

### 4. EmploymentSection Component

Professional experience showcase with timeline-style layout.

#### Features:
- **Company Information**: Logos, names, and positions
- **Achievement Lists**: Bullet points for key accomplishments
- **Gradient Styling**: Consistent design language
- **Responsive Layout**: Mobile-optimized experience
- **Professional Presentation**: Clean, easy-to-scan format

### 5. CertificatesSection Component

A showcase of professional certifications and achievements.

#### Features:
- **Certification Cards**: Individual cards for each certification
- **Badge Display**: AWS and other certification badges
- **Gradient Backgrounds**: Consistent visual styling
- **Responsive Grid**: Adapts to different screen sizes
- **Professional Credibility**: Highlights technical credentials

## Installation & Setup

### Required Dependencies

```bash
npm install framer-motion lucide-react react-router-dom
```

### Core Dependencies
- **React**: ^18.3.1
- **React Router DOM**: ^6.30.0  
- **Framer Motion**: Latest version for animations
- **Lucide React**: For consistent iconography
- **Tailwind CSS**: For utility-first styling
- **TypeScript**: For type safety

## Usage Patterns

### Component Importing
```tsx
// Import individual components
import { Navbar, Footer, ProjectCard } from './components';

// Import home sections
import { HeroSection, AboutSection, SkillsSection } from './components/home';
```

### Theme Context
```tsx
import { ThemeProvider } from './contexts/ThemeContext';

function App() {
  return (
    <ThemeProvider>
      {/* Your components */}
    </ThemeProvider>
  );
}
```

## Design System

### Color Palette
- **Primary Gradient**: Blue (#3B82F6) to Purple (#9333EA)
- **Gray Scale**: Comprehensive gray palette for light/dark modes
- **Semantic Colors**: Success, warning, and error states

### Typography
- **Headings**: Gradient text effects for visual hierarchy
- **Body Text**: Optimized for readability in both themes
- **Font Weights**: Strategic use of font weights for emphasis

### Spacing & Layout
- **Consistent Spacing**: Tailwind's spacing scale
- **Responsive Breakpoints**: Mobile-first approach
- **Grid Systems**: Flexible grid layouts for content

## Accessibility Features

All components include:
- **ARIA Labels**: Comprehensive ARIA attribute usage
- **Keyboard Navigation**: Full keyboard accessibility
- **Focus Management**: Proper focus indicators and management
- **Screen Reader Support**: Semantic HTML and descriptive text
- **Color Contrast**: WCAG compliant color combinations
- **Semantic HTML**: Proper use of HTML5 semantic elements

## Performance Optimizations

- **Lazy Loading**: Images and components load efficiently
- **Animation Performance**: Hardware-accelerated animations
- **Bundle Optimization**: Tree-shakeable exports
- **Responsive Images**: Optimized image delivery
- **Code Splitting**: Components can be loaded on demand

## Browser Support

- **Modern Browsers**: Chrome, Firefox, Safari, Edge (latest versions)
- **Mobile Browsers**: iOS Safari, Chrome Mobile, Samsung Internet
- **Progressive Enhancement**: Graceful degradation for older browsers
- **Touch Devices**: Optimized for mobile and tablet interaction

## SEO Optimization

- **Semantic HTML**: Proper HTML5 semantic structure
- **Meta Information**: Structured data for search engines
- **Accessibility**: Screen reader optimization improves SEO
- **Performance**: Fast loading times benefit search rankings
