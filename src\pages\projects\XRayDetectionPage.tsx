import { Brain, Database, BarChart as Chart, Shield, Code2, Server } from 'lucide-react';
import { ProjectDetailPage } from '../../components';
import type { ProjectDetail } from '../../components/ProjectDetailPage';

const xrayProject: ProjectDetail = {
  title: "Chest X-ray Pneumonia Detection",
  image: {
    src: "https://res.cloudinary.com/dramsks0e/image/upload/v1750755613/Medical-AI-X-ray_bcln1g.png",
    alt: "X-Ray Detection System"
  },
  tags: ["Python", "TensorFlow", "Keras", "Deep Learning"],
  description: "An AI-powered diagnostic tool using deep learning to automatically detect pneumonia from chest X-ray images. The system achieves high accuracy and provides rapid screening capabilities to assist healthcare professionals in diagnosing pneumonia efficiently and accurately.",
  features: [
    {
      icon: <Brain className="w-6 h-6" />,
      title: "Deep Learning Model",
      description: "MobileNet-based CNN architecture optimized for medical image analysis"
    },
    {
      icon: <Database className="w-6 h-6" />,
      title: "Dataset Handling",
      description: "Advanced data preprocessing and augmentation techniques"
    },
    {
      icon: <Chart className="w-6 h-6" />,
      title: "Performance Metrics",
      description: "Comprehensive evaluation using precision, recall, and ROC curves"
    },
    {
      icon: <Shield className="w-6 h-6" />,
      title: "Validation",
      description: "Rigorous testing and validation on diverse X-ray datasets"
    },
    {
      icon: <Code2 className="w-6 h-6" />,
      title: "Clean Implementation",
      description: "Well-documented, modular code following best practices"
    },
    {
      icon: <Server className="w-6 h-6" />,
      title: "Scalable Design",
      description: "Efficient processing pipeline for handling large datasets"
    }
  ],
  technicalDetails: [
    "Implemented using TensorFlow 2.0 and Keras",
    "MobileNet architecture with transfer learning",
    "Data augmentation for improved model generalization",
    "Class balancing through oversampling",
    "Comprehensive evaluation metrics and visualization",
    "Modular codebase with clear documentation",
    "Efficient data processing pipeline"
  ],
  results: [
    "High accuracy on test dataset",
    "Balanced precision and recall metrics",
    "Strong ROC curve performance",
    "Validated against multiple datasets"
  ]
};

function XRayDetectionPage() {
  return <ProjectDetailPage project={xrayProject} />;
}

export default XRayDetectionPage;
