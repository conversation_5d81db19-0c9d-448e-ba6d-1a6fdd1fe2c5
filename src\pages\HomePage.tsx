import ProjectGrid from '../components/ProjectGrid';
import { projects } from '../data/projects';
import {
  HeroSection,
  AboutSection,
  EmploymentSection,
  CertificatesSection,
  SkillsSection
} from '../components/home';

function HomePage() {
  return (
    <main className="min-h-screen bg-gradient-to-b from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 transition-colors duration-300">
      {/* Hero Section */}
      <HeroSection />

      {/* About Section */}
      <AboutSection />

      {/* Employment Section */}
      <EmploymentSection />

      {/* Certificates Section */}
      <CertificatesSection />

      {/* Skills Section */}
      <SkillsSection />

      {/* Projects Section */}
      <ProjectGrid
        projects={projects.filter(p => p.featured)}
        title="Featured Projects"
        subtitle="Showcasing my latest work in full-stack development, AI/ML, and modern web technologies"
        maxItems={4}
        className="bg-white dark:bg-gray-900"
      />
    </main>
  );
}

export default HomePage;