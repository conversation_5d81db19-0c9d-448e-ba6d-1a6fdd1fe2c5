# Classifying Chest X-Ray Images:

# Empowering Healthcare with AI-driven Approach

## <PERSON><PERSON>^1

## Other group members:

## <PERSON>^2 , <PERSON>^3 , <PERSON><PERSON>^4

Abstract. Pneumonia, a global health threat, demands swift and ac-
curate detection for timely interventions. Traditional diagnostic tools,
notably chest X-rays, are susceptible to misclassifications, urging
the need for advanced solutions. This study delves into the trans-
formative potential of Convolutional Neural Networks (CNNs) for
pneumonia detection, employing the pre-trained MobileNet model.
Inspired by a Kaggle project, methodologies are refined with strate-
gic adjustments, encompassing data augmentation and hyperparam-
eter fine-tuning. Approaches such as class weights, SMOTE, and
ADASYN are considered to address dataset imbalance, with a pref-
erence for data augmentation for balance. A thorough approach is
taken in the experiments with a dataset of 5,873 chest X-ray im-
ages. Handling imbalances is prioritized for optimal model training.
A carefully selected batch size 16, coupled with data augmentation
during training, enhances model robustness. The MobileNet archi-
tecture, both with adjustments and without pre-training, is explored,
revealing that starting from scratch outperforms ImageNet weights
in robustness. The model, compiled with Adam optimizer and binary
cross-entropy loss, achieves an impressive accuracy of 96% with a
22% loss over 63 epochs.

## 1 Introduction

Pneumonia, a life-threatening lung infection, claimed 2.5 million
lives globally in 2019, with children under five constituting nearly
a third of the victims [4]. The urgency of swift and accurate diag-
nosis is evident, especially considering the infectious nature of this
ailment. Despite chest X-rays being the primary diagnostic tool, the
prevalence of misclassifications underscores the need for advanced
solutions. Integrating artificial intelligence (AI) into chest X-ray de-
tection represents a transformative approach to healthcare. This re-
search delves into the potential of Convolutional Neural Networks
(CNNs), specifically exploring the adaptability of the pre-trained AI
model named MobileNet. The central objective of this study is to

(^1) School of Computing and Mathematical Sciences, University of Greenwich,
London SE10 9LS, UK, email: <EMAIL>
(^2) School of Computing and Mathematical Sciences, University of Greenwich,
London SE10 9LS, UK, email: <EMAIL>
(^3) School of Computing and Mathematical Sciences, University of Greenwich,
London SE10 9LS, UK, email: <EMAIL>
(^4) School of Computing and Mathematical Sciences, University of Greenwich,
London SE10 9LS, UK, email: <EMAIL>
significantly enhance the classification accuracy of chest X-ray im-
ages, explicitly differentiating between pneumonia and normal cases.
Drawing inspiration from existing code sourced from Kaggle [9],
particularly utilizing MobileNet, we embark on a journey of refine-
ment to optimize performance. Beyond replicating the original code,
our study introduces strategic adjustments, encompassing data aug-
mentation techniques during training to bolster model generalization.
Fine-tuning of hyperparameters becomes a pivotal aspect of our opti-
mization strategy. This involves a nuanced exploration of parameters
such as the learning rate, influencing the convergence and stability of
the training process; the number of epochs, determining how many
times the entire training dataset is passed through the model; batch
size, indicating the number of training examples in one iteration; and
the dropout rate, a regularization hyperparameter preventing over-
fitting. Additionally, we employ early stopping as a regularization
technique, ensuring training halts when the model’s performance on
a validation set starts degrading. The class imbalance in the dataset
is systematically addressed in three ways. First, by applying class
weights to the minority class, we instruct the machine to give more
importance to pneumonia images [3]. Second, we generate new aug-
mented images solely on the training data, achieving a balanced 1:
ratio in both classes [10]. Third, we employ advanced techniques
such as Synthetic Minority Oversampling Technique (SMOTE) [2]
and Adaptive Synthetic (ADASYN) [5] to generate synthetic minor-
ity class samples and address more challenging instances. The sig-
nificance of our research lies not only in its potential technological
advancements but also in the tangible human impact—each misdiag-
nosis avoided translates to a potential life saved. This study seeks to
contribute to the ongoing discourse surrounding AI’s pivotal role in
medical imaging, particularly within the critical domain of pneumo-
nia diagnosis. To offer a comprehensive understanding of our cho-
sen methodology, we will elucidate the rationale behind the selection
of MobileNet, delve into its architectural nuances, and meticulously
detail the adjustments we implement, including the incorporation of
transfer learning. Furthermore, we will explore the intricacies of data
augmentation, shedding light on how it propels superior model gen-
eralization. Navigating the realms of AI in medical imaging, our
research strives to provide a technical contribution surrounding the
implementation of such technologies in healthcare. Recognizing the
transformative potential of AI in enhancing diagnostic accuracy and
addressing healthcare disparities, particularly in regions with limited
access to specialized medical professionals, we aim to bridge the


gap between technology and healthcare outcomes. Our research envi-
sions a future where AI augments, rather than replaces, the expertise
of medical professionals—ensuring timely and accurate diagnoses,
especially in the realm of pneumonia detection, and emphasizing the
profound impact of such advancements on global health outcomes.

## 2 Background

The genesis of this project finds inspiration in a Kaggle endeavor,
offering a beginner-friendly approach to constructing a medical im-
age classification model with a specific focus on pneumonia detec-
tion [9]. Operating at the intersection of artificial intelligence and
deep learning techniques, this project centers around a binary (two-
class) classification task, distinguishing between pneumonia and nor-
mal chest X-ray images. The Kaggle project, serves as a robust guide
in medical image classification, encompassing critical steps like data
loading, preprocessing, model selection, training, and evaluation. By
leveraging artificial intelligence and deep learning, we aim to con-
tribute to the ongoing discourse surrounding efficient and precise
medical image classification, focusing on pneumonia. Our dataset
comprises validated Chest X-ray images, as detailed and analyzed
in ”Deep learning-based classification and referral of treatable hu-
man diseases,” split into training, testing, and validation sets [7]. The
objectives of our project are twofold: first, to develop a robust medi-
cal image classification model for pneumonia detection, and second,
to address the inherent challenges posed by imbalanced data. The
significance of accurate pneumonia detection cannot be overstated,
particularly in the context of timely intervention and treatment. Our
work aligns with the broader goals of our coursework, aiming to hone
technical skills in artificial intelligence and deep learning and apply
these skills to solve real-world medical challenges. By navigating
the complexities of imbalanced data and medical image classifica-
tion, we seek to contribute to advancing healthcare technologies and,
ultimately, improving patient outcomes. In subsequent sections, we
will delve into the specific methodologies employed, emphasizing
our strategies for tackling data imbalance, fine-tuning models, and
rigorously evaluating performance. Through this project, we aspire
to develop a sophisticated pneumonia detection model and contribute
valuable insights to the larger field of medical image classification.

## 3 Experiments and results

In order to determine pneumonia in chest X-ray images, it is essen-
tial to have a thoroughly documented dataset comprising a sufficient
number of images depicting both normal and pneumonia cases. En-
suring accurate labelling is paramount, as it instructs the machine on
the content of each image. Subsequently, the dataset is loaded into the
machine, and the instances are categorized into training, testing, and
validation sets. This distribution is crucial for optimal model training,
with most images allocated for training, a subset for validation during
training, and some reserved for evaluating the model’s performance
on unseen data. Our dataset, comprising 5,873 images, exhibits an
imbalanced pneumonia-to-normal ratio of 3:1 (4,282 pneumonia and
1,591 normal) 1. Recognizing the potential pitfalls of imbalanced
data, we address this issue to prevent bias in the model’s learning.
An imbalanced dataset could lead to skewed decisions, as the model
may prioritize one class over another. While a model may exhibit
seemingly robust learning metrics during training, it may falter dur-
ing testing, having consistently learned to predict the same class.
To rectify this imbalance, we explore three distinct approaches. The
first involves an algorithmic adjustment, assigning weights to images

```
and instructing the machine to accord more attention to the minority
class.
```
```
Figure 1. The distribution of the original dataset
```
```
We calculate the ratio between each class and provide correspond-
ing weights for training [3]. The second technique, Synthetic Mi-
nority Oversampling Technique (SMOTE), generates synthetic in-
stances of the minority class by combining features of the selected
instance with features of its k-nearest neighbors [2]. Additionally,
we experiment with Adaptive Synthetic (ADASYN), an extension
of SMOTE, introducing steps to handle adaptive generation based
on local data distribution and the difficulty of learning instances [5].
The chosen method for balancing the dataset involves the powerful
data augmentation technique. This involves artificially increasing the
diversity of the training dataset by applying transformations such as
rotation, zooming, and flipping to generate 2,535 images of the mi-
nority class 2. It is crucial to note that data augmentation should ex-
clusively be applied to the training data to avoid inadvertently biasing
the model during the evaluation of unseen data [10]. All these ap-
proaches are a part of the oversampling technique, which means bal-
ancing the class distribution by generating or duplicating instances
of the minority class, making it more proportionate to the majority
class.
```
```
Figure 2. Generating augmented images.
```
```
After balancing the dataset, we split it into training (80%), testing
(15%), and validation (5%) sets, ensuring a balance between suffi-
cient training data, unbiased model evaluation, and practical hyper-
parameter tuning. Shuffling the data precedes training to randomize
the order of instances, preventing the model from memorizing spe-
cific patterns related to instance order. Prior to commencing model
training, we undertook a preprocessing phase. This involved resiz-
ing the images to 224x224, converting grayscale images to 3D RGB
images, and normalizing pixel values to a range between 0 and 1.
Each image was labelled as 0 for normal and 1 for pneumonia. These
preprocessing steps ensure that the input data is suitably format-
ted for training and evaluating a machine-learning model. With the
data prepared, parameters were set for model training, including a
batch size representing the number of training examples processed
```

in one iteration. This parameter dictates the number of samples pro-
cessed per model update step. Our experimentation involved varying
batch sizes from 16 to 64, with a careful analysis leading us to se-
lect a batch size 16. This choice, while outperforming others, also
proved memory-efficient. It is noteworthy, however, that a smaller
batch size extended the training time. Subsequently, we implemented
data augmentation during training. This technique randomly alters
images to enhance model robustness, improve generalization, and
mitigate overfitting—where the model may overly memorize train-
ing data, capturing noise rather than underlying patterns. Moving
forward, we constructed our model using MobileNet, a pre-trained,
lightweight, and efficient deep learning model optimized explicitly
for efficiency in terms of model size, computational requirements,
and inference speed. The model architecture comprises depthwise
separable convolution blocks, incorporating depthwise convolutions,
pointwise convolutions, and activation functions. The network con-
cludes with global average pooling, reducing spatial dimensions to
a single value per channel. This representation undergoes processing
by a dense layer, followed by a softmax activation function for class
probability determination [6]. While transfer learning from the Ima-
geNet dataset, a large-scale visual recognition dataset, generally aids
faster convergence during training, our experiments revealed that
starting the model from scratch outperformed pre-trained weights.
Although more training rounds were necessary, the scratch-started
model proved more robust to new, unseen images [8]. Additionally,
we modified the base architecture by excluding the final dense layer
and softmax activation, introducing a global average pooling layer, a
dense layer with 256 neurons and ”relu” activation, a dropout layer
for preventing overfitting, and a final dense layer with one neuron
and ”sigmoid” activation for model predictions.

```
No Weights ImageNet Weights
No. Epochs 63 38
Accuracy 96.36% 94.96%
Loss 0.2220 0.
Precision 96.30% 94.1573%
Recall 99.05% 99.7619%
F1-score 97.6526 96.
AUC 0.9869 0.
Table 1. Model comparison
```
The model was compiled with the Adam optimizer and binary
cross-entropy loss function, apt for binary classification. As depicted
in Table 1, our model achieved a remarkable accuracy of 96% and
a loss of 19% over 63 epochs. In comparison, utilizing ImageNet
weights yielded a 95% accuracy and a loss of 38% within 38 epochs
before signs of overfitting. This suggests that while ImageNet
weights expedite convergence, allowing the model to start from
scratch renders it more resilient to new, unseen images. Figure 3
shows a detailed breakdown of the predictions, highlighting where
they succeeded and where they made errors. Post-training, we eval-
uated the training data of the model trained using cross-validation.
Cross-validation is a prominent technique for estimating the accurate
predictive performance of models and fine-tuning their parameters.
It plays a crucial role in assessing the reliability of machine learning
models by employing various data resampling methods to contribute
to a more robust understanding of a model’s predictive capability
and enhance its generalizability to new, unseen data [1].

```
(a) Confusion matrix - no weights (b) Confusion matrix - ImageNet
```
```
Figure 3. Prediction Comparison.
```
```
We experimented with the possibility of generating one unified
model and combining the predictions to create a better model. In
table 2 we can see the results of the model combination. After de-
veloping four distinct models, we merge their outcomes to enhance
accuracy. The algorithm assesses predictions from each model, re-
turning the most probable result using the mean. However, we de-
veloped a different solution named majority and weights that gives
as an output the most frequent result. In case of a draw between the
models’ predictions, we will apply weights based on the strength of
each model.
```
```
model1 model2 model3 model4 m m and w
w.p 44 35 33 30 25 27
Table 2. Combination of models - w.p = wrong prediction, m = mean, m
and w = majority and weights
```
## 4 Discussion

```
The accurate and timely detection of pneumonia is paramount
in ensuring patients receive appropriate medical interventions
promptly. Any misdiagnosis or delayed diagnosis could lead to
incorrect treatment plans, potentially jeopardizing patient health.
Despite the constraints of a short timeframe, limited resources, and
a relatively small dataset for training, our model has demonstrated
impressive accuracy, achieving 96%. While this is a promising
outcome, acknowledging the existing limitations points towards
avenues for further improvement. A study by Reshan et al. (2023)
presented a methodology for pneumonia detection from chest X-ray
images utilizing eight pre-trained models, including MobileNet.
The authors conducted simulations on two datasets, one comprising
5,856 images and the other a more extensive dataset with 112,
chest X-ray images. Notably, the MobileNet model exhibited the
highest accuracy, achieving 94.23% and 93.75% on the two distinct
datasets [11]. This aligns with our experiment’s findings, particu-
larly in choosing a batch size of 16 over 32 and 64 as an optimal
hyperparameter. The insights gained from Reshan et al.’s work
have been instrumental in our research, providing inspiration and
a reference point for parameter adjustments and decision-making
processes. The alignment in results regarding the choice of batch
size emphasizes the reproducibility and consistency of findings
across different studies. However, it is essential to note that while
our model demonstrates a high accuracy level, ongoing exploration
and refinement of parameters, such as batch size, could yield further
enhancements.
```

## 5 Conclusion and future work

Throughout this research, we meticulously trained and evaluated
over 30 models, exploring many parameters, including batch size,
data augmentation, layer manipulations, optimization and loss func-
tions, number of epochs, data distribution, and learning rates. While
we have delved into key aspects of Convolutional Neural Networks
(CNNs), the realm of CNNs remains an evolving field with much
left to uncover. More time, extensive experimentation, and in-depth
research are imperative to deepen our understanding and attain more
robust results. Achieving an accuracy of 96% is undoubtedly a note-
worthy accomplishment. However, there is an endless quest for im-
provement in the critical domain of medical applications, where hu-
man lives are at stake. The ever-evolving landscape of artificial intel-
ligence in medicine presents an exciting frontier, offering immense
potential to enhance decision-making processes. Looking ahead, we
acknowledge that there is no singular solution or ”silver bullet.” Fu-
ture work leads to a continued exploration of our model’s architec-
ture to unearth avenues for improvement. We can refine the model
further by delving deeper into the complexities of each component
and understanding their specific impacts on our use case. This re-
search represents a significant step forward, but the journey contin-
ues. Pursuing advancements in AI for medical decision-making is not
only a scientific endeavor but a commitment to improving healthcare
outcomes. As we navigate the detailed intersections of technology
and medicine, the potential for transformative breakthroughs remains
compelling.

## ACKNOWLEDGEMENTS

I want to express my sincere gratitude to our dedicated tutors for
their unwavering support and for making the subject educational and
truly exciting. Their passion for the topic has been infectious, pro-
pelling me into a deeper understanding and appreciation. A heartfelt
thank you also goes to my exceptional group members. Our collabo-
rative effort and effective teamwork have significantly enriched this
project. I am confident that this group will continue to excel in future
endeavors, and I look forward to the opportunity to work together
again. Together, we have created not just a report but a foundation
for successful collaboration in the future.

## REFERENCES

```
[1] Daniel Berrar, ‘Cross-validation’, inEncyclopedia of Bioinformatics
and Computational Biology, eds., Shoba Ranganathan, Michael Grib-
skov, Kenta Nakai, and Christian Schonbach, 542–545, Academic ̈
Press, Oxford, (2019).
[2] Nitesh V Chawla, Kevin W Bowyer, Lawrence O Hall, and W Philip
Kegelmeyer, ‘Smote: synthetic minority over-sampling technique’,
Journal of artificial intelligence research, 16 , 321–357, (2002).
[3] Yin Cui, Menglin Jia, Tsung-Yi Lin, Yang Song, and Serge Belongie,
‘Class-balanced loss based on effective number of samples’, inPro-
ceedings of the IEEE/CVF Conference on Computer Vision and Pattern
Recognition (CVPR), (June 2019).
[4] B. Dadonaite and M. Roser. Pneumonia, 2019. Accessed: 07 December
2023.
[5] Haibo He, Yang Bai, Edwardo A Garcia, and Shutao Li, ‘Adasyn:
Adaptive synthetic sampling approach for imbalanced learning’, in
2008 IEEE international joint conference on neural networks (IEEE
world congress on computational intelligence), pp. 1322–1328. Ieee,
(2008).
[6] Andrew G Howard, Menglong Zhu, Bo Chen, Dmitry Kalenichenko,
Weijun Wang, Tobias Weyand, Marco Andreetto, and Hartwig Adam,
‘Mobilenets: Efficient convolutional neural networks for mobile vision
applications’,arXiv preprint arXiv:1704.04861, (2017).
```
```
[7] Daniel Kermany, Kang Zhang, Michael Goldbaum, et al., ‘Labeled op-
tical coherence tomography (oct) and chest x-ray images for classifica-
tion’,Mendeley data, 2 (2), 651, (2018).
[8] Alex Krizhevsky, Ilya Sutskever, and Geoffrey E Hinton, ‘Imagenet
classification with deep convolutional neural networks’,Communica-
tions of the ACM, 60 (6), 84–90, (2017).
[9] KHAN FASHEE MONOWAR, ‘Medical image classification for be-
ginner’. Kaggle project, 2020.
[10] Luis Perez and Jason Wang. The effectiveness of data augmentation in
image classification using deep learning, 2017.
[11] Mana Saleh Al Reshan, Kanwarpartap Singh Gill, Vatsala Anand,
Sheifali Gupta, Hani Alshahrani, Adel Sulaiman, and Asadullah
Shaikh, ‘Detection of pneumonia from chest x-ray images utilizing mo-
bilenet model’, inHealthcare, volume 11, p. 1561. MDPI, (2023).
```

