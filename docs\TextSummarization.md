# Dispersive Flies Optimization:

# A Swarm Intelligence Approach to Text Summarization


### <PERSON><PERSON> Itzhak Per<PERSON>z^1

### 1 Introduction

Text summarization has become an increasingly important tool
in processing large volumes of information, particularly with the
growth of digital content. It aims to condense textual data into a con-
cise format while preserving the essential meaning and context. Tra-
ditional summarization methods often rely on statistical or heuristic
approaches that lack adaptability, whereas optimization-based meth-
ods offer more dynamic and effective solutions for extractive sum-
marization tasks.
Dispersive Flies Optimization (DFO), a swarm-inspired meta-
heuristic, has demonstrated significant potential in solving optimiza-
tion problems due to its simplicity and lightweight computational re-
quirements [1]. Unlike algorithms such as Genetic Algorithms (GA)
and Particle Swarm Optimization (PSO), DFO leverages a decentral-
ized structure, balancing exploration and exploitation through dis-
persal and attraction mechanisms. This balance makes it a promising
candidate for complex problems like sentence selection in summa-
rization tasks.
This report explores the application of DFO in text summarization

(^1) School of Computing and Mathematical Sciences, University of Greenwich,
London SE10 9LS, UK, email: <EMAIL>
and evaluates its effectiveness compared to existing methods such as
Genetic Algorithm with Hierarchical Clustering (GA-HC) and Parti-
cle Swarm Optimization with Hierarchical Clustering (PSO-HC) [5].
The experiments use the CNN/DailyMail dataset, a widely recog-
nized benchmark for summarization research, with ROUGE metrics
used to evaluate the generated summaries’ quality.
This study details the implementation of DFO-based summariza-
tion methods, introduces an enhanced variant incorporating elitism
and local search, and compares their results against GA-HC and
PSO-HC. The findings provide insights into the potential of swarm-
inspired optimization for text summarization.

### 2 Background

```
Extractive text summarization, the process of identifying and extract-
ing the most relevant sentences from a document, has seen significant
advancements with the integration of natural computing and swarm
intelligence techniques. Inspired by collective behavior observed in
nature and abstracted into algorithms, these methods excel at tackling
optimization problems, including those classified as NP-Hard, where
finding the optimal solution requires exploring a vast and complex
solution space.
```
### 2.1 Text Summarization and Optimization

```
Optimization-based approaches to summarization aim to find the
best combination of sentences that maximize coverage and coher-
ence while minimizing redundancy. Traditional methods often rely
on heuristic or statistical techniques, which may struggle with scal-
ability and adaptability. In contrast, natural computing techniques,
such as GA and PSO, offer dynamic mechanisms for solving com-
plex optimization tasks.
GA-HC and PSO-HC were introduced as methods where hierar-
chical clustering was combined with optimization algorithms to en-
hance diversity in summaries. These methods used features like TF-
IDF scores, sentence position, and semantic similarity to represent
sentences and guide optimization, demonstrating their effectiveness
in improving summary quality and achieving competitive ROUGE
scores in their experiments [5].
```
### 2.2 Dispersive Flies Optimization (DFO)

```
DFO is a lightweight swarm intelligence technique designed for
high-dimensional optimization problems. Unlike GA and PSO, DFO
operates with a minimalist structure, where each ’fly’ represents a
candidate solution, and movement is determined by neighboring and
```

global best solutions [1]. This simplicity reduces computational costs
while maintaining an effective balance between exploration and ex-
ploitation.
Given its adaptability and lightweight nature, we propose DFO
as a promising approach for text summarization tasks. Specifically,
DFO could effectively optimize sentence selection, with flies explor-
ing the feature space defined by sentence characteristics (e.g., length,
position, semantic similarity) and the optimization process iteratively
adjusting these weights to identify the most representative sentences.

### 2.3 Advancements and Related Work

Recent developments in optimization-based summarization include
Cat Swarm Optimization (CSO) and hybrid approaches integrating
deep learning. CSO frames summarization as a constrained optimiza-
tion problem, improving sentence selection diversity through seeking
and tracing modes. Evaluated on DUC-2001 and DUC-2002 datasets,
CSO achieved higher ROUGE scores than traditional GA and PSO
methods [3].
Glowworm Swarm Optimization (GSO) has been applied to
domain-specific tasks like legal text summarization, combining opti-
mization with neural models to improve relevance and coherence [7].
While hybrid methods like GSO demonstrate potential, their reliance
on deep learning introduces computational overhead.
Large language models (LLMs) like ChatGPT excel in semantic
understanding and reasoning for extractive summarization [9]. How-
ever, their pre-training and computational demands contrast with the
lightweight nature of swarm-based algorithms like DFO.

### 2.4 Positioning of Current Work

This project builds on Dispersive Flies Optimization (DFO) foun-
dations and extends its application to extractive text summarization.
Unlike prior work integrating clustering into the summarization pro-
cess, our approach directly selects sentences based on optimized
weights calculated using DFO and its enhanced variant, DFO with
Elitism and Local Search (DFO-Elite-Local).
The proposed methods aim to address challenges such as redun-
dancy and relevance by optimizing a fitness function that incor-
porates ROUGE metrics, relevance, and length penalties. This di-
rect and linear pipeline—preprocessing, optimization, and sentence
selection—offers a streamlined alternative to clustering-based ap-
proaches. By focusing on optimization techniques, this work eval-
uates the effectiveness of DFO and its enhancements in producing
concise, coherent, and diverse summaries.

### 3 Problem Formulation

### 3.1 Solution Encoding

Let documentDconsist ofnsentences:D={s 1 ,s 2 ,...,sn}.
Each solutionXin the DFO population is encoded as:

X= [w 1 ,w 2 ,...,wm]∈[0,1]m

Where:

- mis the number of features for each sentence.
- wirepresents the weight for featurei.
- Each weight is constrained to[0,1].

### 3.2 Fitness Function

```
The fitness functionF(X)combines three components:
```
```
F(X) =α·R(X) +β·ROUGE(X) +γ·(1−L(X))
```
```
Where:
```
- R(X): Relevance score.
- ROUGE(X): ROUGE evaluation score.
- L(X): Length penalty.
- α,β,γ: Weight parameters (default:α= 0. 3 ,β= 0. 7 ,γ= 0. 5 ).

#### Component Definitions:

```
Relevance Score:
R(X) =mean(S[selectedindices])
where:
```
- S=D·X(sentence scores).
- selectedindices=topkhighest scoring sentences.
- k=⌊n×targetratio⌋(target summary length).
Length Penalty:
L(X) = 1−exp

##### 

```
−|len(summary)max(k,1)−k|
```
##### 

```
ROUGE Score:
ROUGE(X) =mean(
```
##### P

```
ROUGEi- F1)
wherei∈{ 1 , 2 , 3 ,L}
```
### 3.3 Constraints

1. Solution Bounds:
    ∀i∈{ 1 ,...,m}: 0≤wi≤ 1.
2. Summary Length:
    |selectedsentences|=⌊n×.targetratio⌋
    where targetratio= 0. 08 (≈ 3. 72 sentences)
3. Sentence Order:
    ∀i,j ∈ selectedindices :i < j ⇒ originalposition[i]<
    originalposition[j].

### 3.4 Search Space

```
The search spaceΩis defined as:
Ω = [0,1]^8
Dimensionality and Scale:
```
- Each sentence has eight features, making solution vectors 8-
    dimensional.
- For an average document (29.74 sentences) and average summary
    (3.72 sentences):
       - Possible sentence combinations:C(n,k)wherenis number of
          sentences andkis target summary length,≈ 3 , 654 combina-
          tions for average document.
       - Each feature weight∈[0,1](continuous).
       - Total possible solutions per document:[0,1]^8 × 3 , 654 combi-
          nations.
- Dataset scale:
    - Each document≈ 766 words, 29.74 sentences.
    - All documents (11,487 test set).
    - Total possible solutions across dataset≈[0,1]^8 × 3 , 654 ×
       11 , 490.


### 4 Experiments and Results

### 4.1 Test Plan Overview

The experiments were designed to evaluate the performance of the
proposed algorithms—Dispersive Flies Optimization (DFO) and its
enhanced variant, DFO-Elite-Local—in the context of extractive text
summarization. The objective was to optimize sentence selection by
maximizing a fitness function incorporating ROUGE scores, rele-
vance, and length penalties.
The evaluation metrics included ROUGE-1, ROUGE-2, ROUGE-
3, and ROUGE-L for recall, precision, and F1 scores, primarily em-
phasizing F1 scores, aligning with prior work [5]. Additionally, fit-
ness scores were computed during optimization to guide sentence
selection and provide an internal measure of algorithm performance.
Algorithm [1]: DFO.The base DFO algorithm optimizes weights
for sentence selection using a swarm-inspired mechanism. Candidate
solutions (flies) explore the feature space, starting with a random so-
lution guided by the best-performing solutions in the swarm. The op-
timization process balances exploration (diversifying solutions) and
exploitation (refining high-quality solutions) to converge on optimal
weights.
Algorithm [2]: DFO-Elite-Local.The enhanced algorithm builds
on DFO by incorporating:
1.Elitism: Retaining top-performing solutions across iterations to
preserve high-quality candidates.
2.Local Search: Periodically refining elite solutions using hill
climbing to improve convergence and solution quality.
Both algorithms operate within a streamlined preprocessing, opti-
mization, and sentence selection pipeline.

Algorithm 1DFO

```
1:Initializex ̄i∈[0,1]Dfori= 1→NP
2:whileiteration<maxiterationsdo
3: fori= 1→NPdo
4: s ̄i←documentfeatures· ̄xi
5: selected←topkindices ofs ̄iwherek = ⌈n×
targetratio⌉
6: relevance←mean( ̄si[selected])
7: rouge←calculaterouge(selected)
8: lengthpenalty←exp(−|k−len(selected)|/k)
9: fitness[i] ← α·relevance+β·rouge−γ·(1−
lengthpenalty)
10: end for
11: best←argmax(fitness)
12: fori= 1→NPdo
13: ifi̸=bestthen
14: dist←∥x ̄i− ̄xj∥∀j̸=i
15: nearest←argmin(dist)
16: ifrand< δthen
17: x ̄i←U(0,1)D
18: else
19: x ̄i← ̄xi+r 1 ( ̄xbest−x ̄i) +r 2 ( ̄xnearest−x ̄i)
20: end if
21: ̄xi←clip( ̄xi, 0 ,1)
22: end if
23: end for
24:end while
```
```
Algorithm 2DFO with Elitism & Local Search
1:Initializex ̄i∈[0,1]Dfori= 1→NP
2:nelite←⌈NP×eliteratio⌉
3:whileiteration<maxiterationsdo
4: fori= 1→NPdo
5: s ̄i←documentfeatures· ̄xi
6: selected←topkindices ofs ̄iwherek = ⌈n×
targetratio⌉
7: relevance←mean( ̄si[selected])
8: rouge←calculaterouge(selected)
9: lengthpenalty←exp(−|k−len(selected)|/k)
10: fitness[i] ← α·relevance+β·rouge−γ·(1−
lengthpenalty)
11: end for
12: eliteidx←top nelite indices by fitness
13: elitesolutions← ̄x[eliteidx]
14: ifiteration mod localsearchfreq= 0then
15: forein elitesolutions[1 :topk]do
16: forj= 1→hillclimbiterdo
17: e′←e+N(0,stepsize)
18: e′←clip(e′, 0 ,1)
19: iffitness(e′)>fitness(e)then
20: e←e′
21: end if
22: end for
23: end for
24: end if
25: fori= 1→NPdo
26: ifi /∈eliteidxthen
27: dist←∥x ̄i−x ̄j∥∀j̸=i
28: nearest←argmin(dist)
29: ifrand< δthen
30: x ̄i←U(0,1)D
31: else
32: x ̄i← ̄xi+r 1 ( ̄xbest−x ̄i) +r 2 ( ̄xnearest−x ̄i)
33: end if
34: ̄xi←clip( ̄xi, 0 ,1)
35: end if
36: end for
37: ̄x[eliteidx]←elitesolutions ▷Restore elite solutions
38:end while
```

### 4.2 Parameter Exploration Results

A series of parameter exploration experiments were conducted to an-
alyze the behavior and performance of the DFO algorithm. These
experiments focused on understanding the impact of population size,
correlations between key parameters and metrics, and variations in
the delta parameter.

#### 4.2.1 Convergence Metrics Across Population Sizes and

#### Iterations

Table 1 presents the convergence metrics for different population
sizes and iteration configurations (50, 100, and 150 iterations). This
breakdown provides a more detailed view of how iteration counts
impact the fitness mean for each population size.
Key observations include:

- Larger population sizes (e.g., 90) consistently achieved higher fit-
    ness means across all iterations, with the best performance ob-
    served for 150 iterations (5.6108).
- Smaller population sizes (e.g., 30) showed noticeable improve-
    ment with increasing iteration counts, from 5.6089 (50 iterations)
    to 5.6118 (150 iterations), indicating that higher iterations com-
    pensate for smaller population sizes.
- Intermediate population sizes (50, 70) demonstrated stable per-
    formance but with diminishing improvements as iteration counts
    increased.
- Overall, higher iteration counts generally improved fitness for
    all population sizes, though the gains were more pronounced for
    smaller populations.

```
Table 1. Fitness Mean Across Population Sizes and Iterations - DFO
```
## Population Size Iterations Fitness Mean

## 30 50 5.

## 30 100 5.

## 30 150 5.

## 50 50 5.

## 50 100 5.

## 50 150 5.

## 70 50 5.

## 70 100 5.

## 70 150 5.

## 90 50 5.

## 90 100 5.

## 90 150 5.

#### 4.2.2 Correlation Analysis Between Parameters and

#### Metrics

The relationship between key parameters (e.g., population size, max
iterations) and performance metrics (ROUGE precision and fitness
mean) is presented in Table 2. Notable findings include:

- Weak correlations between population size and ROUGE metrics
    (e.g., ROUGE-1 Precision,r= 0. 2193 ,p > 0. 4 ) indicate that
    increasing population size alone does not significantly impact the
    precision of summaries.
       - Max iterations demonstrated a moderate correlation with
          ROUGE-1 Precision (r= 0. 4456 ), suggesting a potential benefit
          in extending iterations, although the relationship was not statisti-
          cally significant (p > 0. 1 ).
       - Overall, the weak correlations suggest that other factors, such as
          the fitness function or parameter fine-tuning, may play a more crit-
          ical role in influencing performance.

```
Table 2. Correlation Analysis Between Parameters and Metrics - DFO
Parameter Comparison Correlation Coefficient p-value
Population Size vs. ROUGE-1 Precision 0.2193 0.
Population Size vs. ROUGE-2 Precision 0.2647 0.
Population Size vs. ROUGE-L Precision 0.2311 0.
Population Size vs. Fitness Mean 0.0339 0.
Max Iterations vs. ROUGE-1 Precision 0.4456 0.
Max Iterations vs. ROUGE-2 Precision 0.3075 0.
Max Iterations vs. ROUGE-L Precision 0.2454 0.
Max Iterations vs. Fitness Mean 0.1440 0.
```
#### 4.2.3 Delta Parameter and ROUGE Metrics

```
The delta parameter, controlling the randomization probability dur-
ing the optimization process, was varied to study its impact on
ROUGE scores. Figure 4.2.3 illustrates the ROUGE-1, ROUGE-2,
ROUGE-3, and ROUGE-L scores across different delta values.
```
- A clear upward trend was observed as the delta increased, with
    the highest ROUGE scores achieved atδ= 0. 1 for all metrics.
    ROUGE-1 improved from 0.333 (δ= 0. 01 ) to 0.398 (δ= 0. 1 ),
    while ROUGE-L increased from 0.278 to 0.310.
- Lower delta values (δ= 0. 01 ) resulted in lower scores, likely due
    to insufficient exploration during the optimization process.
- The improvements across ROUGE-2 and ROUGE-3 were more
    subtle, reflecting the greater difficulty in optimizing higher-order
    n-gram metrics.

##### 0.01 0.028 0.046 0.064 0.082 0.

##### 0

##### 0. 2

##### 0. 4

```
Delta
```
```
ROUGE Score
```
##### ROUGE-1 ROUGE-2 ROUGE-3 ROUGE-L

### 4.3 Algorithmic Enhancements and Results

```
This section explores three enhanced Dispersive Flies Optimization
(DFO) algorithm variants: Local Search, Elite Selection, and the
combined Elite + Local Search. These enhancements improve the al-
gorithm’s ability to generate high-quality summaries by fine-tuning
solutions (Local Search) and preserving top-performing candidates
(Elite Selection). The combined approach leverages both strategies
for maximum performance.
```

#### 4.3.1 Parameter Tuning for Elite + Local Search

A heatmap was generated to evaluate the combined Elite + Local
Search variant (Figure 1) showing the interaction between elite per-
centage and local search frequency. The heatmap illustrates that the
combination of a 15% elite percentage and local search frequency ev-
ery 20 iterations achieved the highest ROUGE-1 F1 score (0.2481),
indicating an effective balance between exploration and exploitation.

```
Figure 1. Elitism & Local Search parameter Heatmap
```
#### 4.3.2 Performance Comparison Across Variants

Figure 2 summarizes the average ROUGE-F1 scores across all vari-
ants, directly comparing their performance. The combined Elite +
Local Search variant consistently outperformed the individual en-
hancements, achieving the highest scores for all metrics, includ-
ing ROUGE-1 (0.221), ROUGE-2 (0.089), ROUGE-3 (0.052), and
ROUGE-L (0.167).

##### ROUGE-1 ROUGE-2 ROUGE-3 ROUGE-L

##### 0

##### 0. 05

##### 0. 1

##### 0. 15

##### 0. 2

##### 0. 25

```
0.^221
```
```
0.
```
```
089
```
```
0.
```
```
052
```
```
0.^167
```
```
0.
```
```
218
```
```
0.^087
```
```
0.^051
```
```
0.
```
```
165
```
```
0.
```
```
216
```
```
0.
```
```
085
```
```
0.
```
```
05
```
```
0.
```
```
164
```
```
ROUGE Metric
```
```
F1 Score
```
```
Elite + Local Elite Local
```
```
Figure 2. Comparison of ROUGE F1 Scores Across DFO Variants
```
#### 4.3.3 Convergence Analysis

```
Figure 3 highlights the convergence behavior of all three variants.
The Elite + Local Search variant stabilized faster and achieved higher
final ROUGE-1 scores than the individual enhancements. This rapid
convergence suggests a synergistic effect of the combined strategies.
```
##### 0 20 40 60 80 100

##### 0. 05

##### 0. 1

##### 0. 15

##### 0. 2

```
Iterations
```
```
ROUGE-1 F1 Score
```
```
Elite + Local Elite Local
```
```
Figure 3. Convergence Analysis of DFO Variants (ROUGE-1 F1)
```
#### 4.3.4 Sample Summaries

```
To illustrate the qualitative performance of our summarization sys-
tem, Table 3 presents examples of generated summaries alongside
their human-written references. The ”Good Summary” demonstrates
the system’s ability to retain key details and maintain coherence,
whereas the ”Bad Summary” highlights occasional challenges in
generating semantically relevant content.
```
```
Table 3. Summary Comparison
Generated Summary Reference Human Text
Good
Summary
```
```
The final film featuring the
late Paul Walker, ”Furious 7”
is opening around the globe
this weekend and earned a
record-breaking $60 million
internationally on Wednesday
and Thursday for a possible
worldwide debut approaching
or crossing $300 million by the
end of Easter Sunday.
```
```
The final film featuring the late
Paul Walker, ”Furious 7” is
opening around the globe this
weekend. Its worldwide debut
may approach or cross $
million by the end of Easter
Sunday.
```
```
Bad Sum-
mary
```
```
To say Neil butchered the song
before the Las Vegas Outlaws
Arena Football League game
would be unkind to those in the
profession.
```
```
Singing the national anthem is
a risky proposition. Whitney
Houston nailed it; Roseanne
Barr destroyed it.
```
#### 4.3.5 Comparison with Main Paper

```
Figures 4.3.5, 4.3.5, 4.3.5 compares the ROUGE Precision, Recall,
and F1-Score metrics across DFO-based methods and GA-HC/PSO-
HC, as proposed in [5].
```

##### ROUGE-1 ROUGE-2 ROUGE-3 ROUGE-L

##### 0

##### 0. 5

##### 1

```
0.
```
```
288
```
```
0.
```
```
112
0.
```
```
064
```
```
0.
```
(^0187)

.^290

```
0.
```
```
114
0.
```
```
066
```
```
0.
```
```
189
```
```
0.^9800.^9700.^9800.
```
```
990
0.^9400.^9400.
```
```
950
0.^940
```
```
Precision
```
```
Average Precision Comparison
```
```
DFO DFO-Elite-Local GA-HC PSO-HC
```
##### ROUGE-1 ROUGE-2 ROUGE-3 ROUGE-L

##### 0

##### 0. 2

##### 0. 4

##### 0. 6

##### 0. 8

##### 1

```
0.
```
```
45
```
```
0.
```
```
17
0.
```
```
09
```
```
0.
```
```
29
```
```
0.
```
```
45
```
```
0.
```
```
17
0.
```
```
09
```
```
0.
```
```
29
```
```
0.^510
```
.^55
    0.

```
49
```
```
0.^59
0.
```
```
52
0.
```
```
50
0.^48
```
```
0.
```
```
60
```
```
Recall
```
```
Average Recall Comparison
```
```
DFO DFO-Elite-Local GA-HC PSO-HC
```
##### ROUGE-1 ROUGE-2 ROUGE-3 ROUGE-L

##### 0

##### 0. 2

##### 0. 4

##### 0. 6

##### 0. 8

##### 1

```
0.^32
```
```
0.
```
```
12
0.
```
```
07
```
```
0.
```
```
20
```
```
0.
```
```
32
```
```
0.
```
```
12
0.
```
```
07
```
```
0.
```
```
20
```
```
0.
```
```
67
0.
```
```
67
0.
```
```
67
0.
0.^680.^680.^68670.^68
```
```
F1-Score
```
```
Average F1-Score Comparison
```
```
DFO DFO-Elite-Local GA-HC PSO-HC
```
#### 4.3.6 Comparison with Other Work

To contextualize the results, Figure 4.3.6 and Figure 4.3.6 compare
DFO-based methods with [8], which used a 3,000-document subset
of the CNN dataset.

```
DFODFO-ELB&Bw/o GapGapGAP*WGAPWGAP*MsWordSys19Sys31UniRankMRank
```
##### 0

##### 0. 2

##### 0. 4

##### 0. 6

##### 0. 8

```
0.
```
```
635
0.
```
```
639
```
```
0.
```
```
482
0.^4410
```
.^4710.^4790. (^4610).^463
0.
(^4390). 443
0.^4230

.^4400.^462

```
Recall
```
```
ROUGE-1 Recall Comparison
```
```
DFODFO-ELB&Bw/o GapGapGAP*WGAPWGAP*MsWordSys19Sys31UniRankMRank
```
##### 0

##### 0. 1

##### 0. 2

##### 0. 3

##### 0. 4

```
0.
```
(^2560).^259
0.
218
0.^1890.
1910.^206
0.
(^1820). 184
0.
1670.^182
0.
131
0.
176
0.^174
Recall
ROUGE-2 Recall Comparison

### 5 Discussion

### 5.1 Delta Parameter Tuning and Exploration

```
The delta parameter significantly influenced the balance between ex-
ploration and exploitation in DFO. Initial testing was guided by rec-
ommended values in the range of 0.0001–0.01, with 0.001 being a
typical choice for swarm-based optimization tasks [1]. However, ex-
perimental results demonstrated that a higher delta value (e.g., 0.1)
consistently yielded better ROUGE scores across metrics. For in-
stance, ROUGE-1 average scores improved by approximately 15%
when the delta was increased from 0.05 to 0.1 (Fig. 3). This diver-
gence highlights the context-specific nature of delta tuning: in high-
dimensional feature spaces with significant constraints, broader ex-
ploration enabled the algorithm to escape local optima and identify
better solutions.
However, this increased exploration comes with a trade-off:
smaller delta values, while less effective in this scenario, may prove
advantageous for problems with tightly constrained solution spaces.
The results underscore the need for empirical tuning of swarm pa-
rameters in context-specific applications, with future work poten-
tially benefiting from adaptive delta strategies that evolve during op-
timization.
```
### 5.2 Local Search and Elitism Enhancements

```
Local search and elitism were incorporated into DFO to refine and
preserve high-quality solutions. The heatmap analysis (Fig. 1) re-
vealed optimal settings for hill climbing iterations (20) and elitism
```

percentage (15%). These parameters balanced computational effi-
ciency and performance with higher elitism ratios (e.g., 20–25%),
leading to premature convergence and loss of diversity. Experimental
results also indicated that DFO-Elitism+Local Search outperformed
DFO-Elitism by a marginal but consistent margin (e.g., ROUGE-L
F1 improved by 2.5%).
Local search proved particularly effective for fine-tuning top-
ranked solutions, as evidenced by improved precision metrics across
all ROUGE variants. However, its computational overhead was non-
negligible, and future work could explore adaptive local search fre-
quencies to optimize efficiency. Conversely, elitism contributed to
population stability, ensuring high-quality solutions persisted across
iterations. These findings align with prior research emphasizing the
benefits of elitism in maintaining diversity and accelerating conver-
gence [4].

### 5.3 Comparison with Baseline Methods

Compared to GA-HC and PSO-HC [5], our algorithms demon-
strated competitive performance on recall metrics but lagged in pre-
cision and F1 scores. For example, while DFO-Elitism+Local Search
achieved a ROUGE-1 precision of 0.287 (Fig. 2), GA-HC and PSO-
HC scored 0.98 and 0.94, respectively. This disparity can be at-
tributed to two primary factors: the absence of clustering and the use
of spaCy embeddings instead of Word2Vec.
Clustering, as implemented in the baselines, enhances sentence
diversity by grouping semantically similar sentences and selecting
from these clusters. This process likely mitigates redundancy, a lim-
itation observed in our summaries (Table 3). Similarly, Word2Vec
embeddings, which capture richer semantic relationships, may have
provided a more nuanced feature space for optimization. However,
the choice of spaCy was deliberate, as it offered a lightweight, pre-
trained alternative suitable for the project’s time and resource con-
straints [6].

### 5.4 Behavioral Analysis of DFO Variants

The behavioral similarities between the DFO variants suggest that the
core algorithm is robust but leaves limited room for further improve-
ment through minor parameter adjustments. This observation raises
questions about other areas of potential enhancement. For instance,
the initialization of the population, which was entirely random in
this study, may have constrained the algorithm’s ability to explore
promising regions of the feature space. Incorporating cognitive pop-
ulation initialization, which introduces domain-specific biases into
the initial solutions, could improve convergence rates and solution
quality [2].
Additionally, the reliance on simple sentence-level features (e.g.,
length, position, semantic similarity) may have limited the algo-
rithm’s ability to capture the nuanced relationships between sen-
tences fully. Future iterations could benefit from integrating more
advanced embeddings or contextual representations, such as BERT
or GPT-based embeddings, to enrich the feature space.

### 5.5 Evaluation Framework and Metrics

The evaluation relied heavily on ROUGE metrics, which, while
widely used in summarization research, have known limitations.
Specifically, ROUGE scores focus on lexical overlap and may not
fully capture semantic fidelity or coherence. For example, summaries

```
with high recall but low precision often included extraneous content,
highlighting the need for complementary evaluation methods.
Fitness scores, which integrated relevance, ROUGE metrics, and
length penalties, provided a valuable measure of optimization suc-
cess but lacked interpretability outside the algorithmic context. Incor-
porating human evaluations or task-specific metrics, such as seman-
tic similarity or readability, could provide a more holistic summary
quality assessment.
Despite these limitations, the evaluation framework was sufficient
to demonstrate the comparative strengths and weaknesses of the pro-
posed methods. Future studies could explore more diverse datasets
and evaluation scenarios to generalize these findings.
```
### 6 Conclusion and future work

```
This study investigated the application of Dispersive Flies Optimiza-
tion (DFO) for extractive text summarization, presenting both a base-
line implementation and an enhanced variant incorporating elitism
and local search mechanisms. The findings demonstrate the poten-
tial of lightweight swarm intelligence algorithms in tackling summa-
rization tasks, particularly in achieving competitive precision with
reduced computational overhead. By systematically tuning parame-
ters and evaluating algorithmic behaviors, this research highlights the
delicate balance required between exploration and exploitation in op-
timization problems. However, the results also revealed limitations in
precision and F1 scores compared to more sophisticated approaches
like GA-HC and PSO-HC, underscoring the inherent trade-offs in
simplifying the pipeline.
A significant contribution of this work lies in its evaluation frame-
work, which combined ROUGE metrics with fitness analysis to as-
sess algorithmic performance comprehensively. The introduction of
elitism and local search mechanisms stabilized the optimization pro-
cess and refined solutions, although with marginal performance im-
provements. The decision to omit clustering and to use spaCy em-
beddings rather than Word2Vec or BERT was driven by practical
constraints. Still, it likely impacted the richness of feature represen-
tations and the diversity of selected sentences. While streamlining
the implementation, these trade-offs underscore the importance of
balancing resource limitations with algorithmic complexity.
Despite these strengths, this work has its limitations. The random
initialization of the population may have hindered the exploration of
promising regions in the search space, and the reliance on ROUGE
metrics may not fully capture the semantic coherence or contextual
relevance of generated summaries. Furthermore, the absence of clus-
tering may have contributed to redundancy in sentence selection, lim-
iting the overall quality of summaries.
Looking ahead, several avenues for further research emerge from
this study. Integrating more advanced feature extraction methods,
such as contextualized embeddings, could significantly enhance the
algorithm’s ability to capture semantic nuances. Exploring cogni-
tive or biased population initialization could improve convergence
rates by guiding the algorithm toward more promising regions of
the search space. Reintroducing clustering, potentially lightweight,
may address redundancy issues and improve the diversity of selected
summaries without excessive computational costs. Additionally, fu-
ture studies should consider broadening evaluation frameworks to in-
clude human assessments or alternative metrics that better capture
the quality of generated summaries. Testing the algorithm on more
diverse datasets and applying it to other domains could also provide
insights into its generalizability and robustness.
```

### REFERENCES

[1] Mohammad Majid Al-Rifaie, ‘Dispersive flies optimisation’, in 2014
federated conference on computer science and information systems, pp.
529–538. IEEE, (2014).
[2] Muhammad Arif, Jianer Chen, Guojun Wang, and Hafiz Tayyab Rauf,
‘Cognitive population initialization for swarm intelligence and evolu-
tionary computing’,Journal of Ambient Intelligence and Humanized
Computing, 1–14, (2022).
[3] Dipanwita Debnath, Ranjita Das, and Partha Pakray, ‘Single document
text summarization addressed with a cat swarm optimization approach’,
Applied Intelligence, 53 (10), 12268–12287, (2023).
[4] Jieguang He, Zhiping Peng, Jinbo Qiu, Delong Cui, and Qirui Li, ‘A
novel elitist fruit fly optimization algorithm’,Soft Computing, 27 (8),
4823–4851, (2023).
[5] Muhammad Mohsin, Shazad Latif, Muhammad Haneef, Usman Tariq,
Muhammad Attique Khan, Sefedine Kadry, Hwan-Seung Yong, and
Jung-In Choi, ‘Improved text summarization of news articles using ga-hc
and pso-hc’,Applied Sciences, 11 (22), 10511, (2021).
[6] Praneeth Sethumadhavan and Kavya Alluru, ‘Comparative study of pop-
ular word embeddings and deep learning methods for tweets classifi-
cation’, in2023 IEEE Fifth International Conference on Advances in
Electronics, Computers and Communications (ICAECC), pp. 1–6. IEEE,
(2023).
[7] V Vaissnave and P Deepalakshmi, ‘Modeling of automated glowworm
swarm optimization based deep learning model for legal text summariza-
tion’,Multimedia Tools and Applications, 82 (11), 17175–17194, (2023).
[8] Pradeepika Verma, Anshul Verma, and Sukomal Pal, ‘An approach for
extractive text summarization using fuzzy evolutionary and clustering
algorithms’,Applied Soft Computing, 120 , 108670, (2022).
[9] Haopeng Zhang, Xiao Liu, and Jiawei Zhang, ‘Extractive summa-
rization via chatgpt for faithful summary generation’,arXiv preprint
arXiv:2304.04193, (2023).


