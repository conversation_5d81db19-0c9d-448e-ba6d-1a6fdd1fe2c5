import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { ChevronRight, Home } from 'lucide-react';

interface BreadcrumbItem {
  label: string;
  href: string;
  isLast: boolean;
}

const Breadcrumb: React.FC = () => {
  const location = useLocation();

  // Function to convert slug to title case
  const slugToTitle = (slug: string): string => {
    return slug
      .split('-')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  // Function to get display name for specific routes
  const getRouteDisplayName = (segment: string, fullPath: string): string => {
    // Handle specific route mappings
    const routeMap: Record<string, string> = {
      'xray-detection': 'X-Ray Detection',
      'netcommhub': 'NetComm Hub',
      'study-platform': 'Study Platform',
      'text-summarization': 'Text Summarization',
      'projects': 'Projects',
      'about': 'About',
      'blog': 'Blog',
      'contact': 'Contact'
    };

    return routeMap[segment] || slugToTitle(segment);
  };

  // Generate breadcrumb items from current path
  const generateBreadcrumbs = (): BreadcrumbItem[] => {
    const pathSegments = location.pathname.split('/').filter(segment => segment !== '');
    
    // Always start with Home
    const breadcrumbs: BreadcrumbItem[] = [
      {
        label: 'Home',
        href: '/',
        isLast: pathSegments.length === 0
      }
    ];

    // Build breadcrumbs for each path segment
    let currentPath = '';
    pathSegments.forEach((segment, index) => {
      currentPath += `/${segment}`;
      const isLast = index === pathSegments.length - 1;
      
      breadcrumbs.push({
        label: getRouteDisplayName(segment, currentPath),
        href: currentPath,
        isLast
      });
    });

    return breadcrumbs;
  };

  const breadcrumbs = generateBreadcrumbs();

  // Don't show breadcrumbs on home page
  if (location.pathname === '/') {
    return null;
  }

  return (
    <nav
      className="bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-800 transition-colors duration-300"
      aria-label="Breadcrumb"
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
        <ol className="flex items-center space-x-2 text-sm">
          {breadcrumbs.map((item, index) => (
            <li key={item.href} className="flex items-center">
              {index > 0 && (
                <ChevronRight
                  className="w-4 h-4 text-gray-400 dark:text-gray-500 mx-2"
                  aria-hidden="true"
                />
              )}
              
              {item.isLast ? (
                <span
                  className="text-gray-900 dark:text-gray-100 font-medium flex items-center"
                  aria-current="page"
                >
                  {index === 0 && <Home className="w-4 h-4 mr-1" aria-hidden="true" />}
                  {item.label}
                </span>
              ) : (
                <Link
                  to={item.href}
                  className="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100 transition-colors duration-200 flex items-center group"
                  aria-label={`Go to ${item.label}`}
                >
                  {index === 0 && <Home className="w-4 h-4 mr-1 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-200" aria-hidden="true" />}
                  <span className="group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-200">
                    {item.label}
                  </span>
                </Link>
              )}
            </li>
          ))}
        </ol>
      </div>
    </nav>
  );
};

export default Breadcrumb;
