import React from 'react';
import { <PERSON><PERSON>ex<PERSON>, <PERSON>, <PERSON><PERSON><PERSON> as Chart, Code2, Zap, Database } from 'lucide-react';

function TextSummarizationPage() {
  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors duration-300">
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg dark:shadow-gray-900/20 overflow-hidden transition-colors duration-300">
          <img
            src="https://images.unsplash.com/photo-1456324504439-367cee3b3c32?auto=format&fit=crop&w=1600&h=600"
            alt="Text Summarization System"
            className="w-full h-96 object-cover"
          />
          
          <div className="p-8">
            <h1 className="text-4xl font-bold text-gray-900 dark:text-gray-100 mb-4">DFO-Based Text Summarization</h1>

            <div className="flex flex-wrap gap-2 mb-8">
              <span className="px-3 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 rounded-full text-sm">Python</span>
              <span className="px-3 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 rounded-full text-sm">NLP</span>
              <span className="px-3 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 rounded-full text-sm">Machine Learning</span>
              <span className="px-3 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 rounded-full text-sm">Optimization</span>
            </div>

            <div className="prose max-w-none">
              <p className="text-lg text-gray-600 dark:text-gray-300 mb-8">
                An advanced text summarization system using Derivative-Free Optimization (DFO) algorithms
                to generate concise and informative summaries of scientific papers. The system implements
                multiple DFO variants and comprehensive evaluation metrics.
              </p>

              <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-4">Key Features</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                <FeatureCard
                  icon={<Brain className="w-6 h-6" />}
                  title="Multiple DFO Variants"
                  description="Implementation of various optimization algorithms"
                />
                <FeatureCard
                  icon={<FileText className="w-6 h-6" />}
                  title="Text Processing"
                  description="Advanced preprocessing and feature extraction"
                />
                <FeatureCard
                  icon={<Chart className="w-6 h-6" />}
                  title="Evaluation Metrics"
                  description="Comprehensive ROUGE metrics implementation"
                />
                <FeatureCard
                  icon={<Code2 className="w-6 h-6" />}
                  title="Modular Design"
                  description="Clean, maintainable code architecture"
                />
                <FeatureCard
                  icon={<Database className="w-6 h-6" />}
                  title="Efficient Processing"
                  description="Multi-threaded batch processing capabilities"
                />
                <FeatureCard
                  icon={<Zap className="w-6 h-6" />}
                  title="Performance"
                  description="Optimized for speed and accuracy"
                />
              </div>

              <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-4">Technical Details</h2>
              <ul className="list-disc pl-6 text-gray-600 dark:text-gray-300 mb-8">
                <li>Python implementation with spaCy for NLP</li>
                <li>Multiple DFO algorithm variants</li>
                <li>TF-IDF and semantic feature extraction</li>
                <li>ROUGE evaluation metrics</li>
                <li>Multi-threaded processing</li>
                <li>Efficient caching system</li>
                <li>Comprehensive documentation</li>
              </ul>

              <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-4">Results</h2>
              <ul className="list-disc pl-6 text-gray-600 dark:text-gray-300">
                <li>ROUGE-1: ~0.40 F1-score</li>
                <li>ROUGE-2: ~0.17 F1-score</li>
                <li>ROUGE-L: ~0.36 F1-score</li>
                <li>Efficient processing of large datasets</li>
              </ul>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}

interface FeatureCardProps {
  icon: React.ReactNode;
  title: string;
  description: string;
}

function FeatureCard({ icon, title, description }: FeatureCardProps) {
  return (
    <div className="bg-gray-50 dark:bg-gray-700 p-6 rounded-lg transition-colors duration-300">
      <div className="text-blue-600 dark:text-blue-400 mb-3">{icon}</div>
      <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">{title}</h3>
      <p className="text-gray-600 dark:text-gray-300">{description}</p>
    </div>
  );
}

export default TextSummarizationPage;