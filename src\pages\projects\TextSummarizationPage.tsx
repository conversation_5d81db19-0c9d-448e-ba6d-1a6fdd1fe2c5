import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON> as Chart, Code2, Zap, Database } from 'lucide-react';
import { ProjectDetailPage } from '../../components';
import type { ProjectDetail } from '../../components/ProjectDetailPage';

const textSummarizationProject: ProjectDetail = {
  title: "DFO-Based Text Summarization",
  image: {
    src: "https://images.unsplash.com/photo-1456324504439-367cee3b3c32?auto=format&fit=crop&w=1600&h=600",
    alt: "Text Summarization System"
  },
  tags: ["Python", "NLP", "Machine Learning", "Optimization"],
  description: "An advanced text summarization system using Derivative-Free Optimization (DFO) algorithms to generate concise and informative summaries of scientific papers. The system implements multiple DFO variants and comprehensive evaluation metrics.",
  features: [
    {
      icon: <Brain className="w-6 h-6" />,
      title: "Multiple DFO Variants",
      description: "Implementation of various optimization algorithms"
    },
    {
      icon: <FileText className="w-6 h-6" />,
      title: "Text Processing",
      description: "Advanced preprocessing and feature extraction"
    },
    {
      icon: <Chart className="w-6 h-6" />,
      title: "Evaluation Metrics",
      description: "Comprehensive ROUGE metrics implementation"
    },
    {
      icon: <Code2 className="w-6 h-6" />,
      title: "Modular Design",
      description: "Clean, maintainable code architecture"
    },
    {
      icon: <Database className="w-6 h-6" />,
      title: "Efficient Processing",
      description: "Multi-threaded batch processing capabilities"
    },
    {
      icon: <Zap className="w-6 h-6" />,
      title: "Performance",
      description: "Optimized for speed and accuracy"
    }
  ],
  technicalDetails: [
    "Python implementation with spaCy for NLP",
    "Multiple DFO algorithm variants",
    "TF-IDF and semantic feature extraction",
    "ROUGE evaluation metrics",
    "Multi-threaded processing",
    "Efficient caching system",
    "Comprehensive documentation"
  ],
  results: [
    "ROUGE-1: ~0.40 F1-score",
    "ROUGE-2: ~0.17 F1-score",
    "ROUGE-L: ~0.36 F1-score",
    "Efficient processing of large datasets"
  ]
};

function TextSummarizationPage() {
  return <ProjectDetailPage project={textSummarizationProject} />;
}

export default TextSummarizationPage;
