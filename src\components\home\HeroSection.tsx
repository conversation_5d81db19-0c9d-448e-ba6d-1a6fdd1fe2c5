import React from 'react';
import { Linkedin, Mail } from 'lucide-react';

interface SocialLink {
  name: string;
  href: string;
  icon: React.ReactNode;
  ariaLabel: string;
}

const socialLinks: SocialLink[] = [
  {
    name: 'GitHub',
    href: 'https://github.com/PeretzNiro',
    icon: (
      <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
        <path
          fillRule="evenodd"
          d="M12 2C6.477 2 2 6.484 2 12.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0112 6.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.202 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.943.359.309.678.92.678 1.855 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0022 12.017C22 6.484 17.522 2 12 2z"
          clipRule="evenodd"
        />
      </svg>
    ),
    ariaLabel: 'GitHub Profile'
  },
  {
    name: 'LinkedIn',
    href: 'https://www.linkedin.com/in/nir-peretz/',
    icon: <Linkedin className="w-6 h-6" />,
    ariaLabel: 'LinkedIn Profile'
  },
  {
    name: 'Email',
    href: 'mailto:<EMAIL>',
    icon: <Mail className="w-6 h-6" />,
    ariaLabel: 'Email Contact'
  }
];

const HeroSection: React.FC = () => {
  return (
    <header className="bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-800 dark:via-gray-900 dark:to-gray-800 shadow-sm dark:shadow-gray-800/20 transition-colors duration-300">
      <div className="max-w-5xl mx-auto px-4 py-16 sm:px-6 lg:px-8">
        <div className="text-center">
          <img
            className="w-32 h-32 rounded-full mx-auto mb-6 shadow-lg"
            src="https://res.cloudinary.com/dramsks0e/image/upload/v1750756231/avatar.png"
            alt="Nir Peretz - Software Engineer Profile Photo"
            width="128"
            height="128"
            loading="eager"
          />
          <h1 className="text-4xl font-bold text-gray-900 dark:text-gray-100 mb-2">Nir Peretz</h1>
          <p className="text-xl text-gray-600 dark:text-gray-400 mb-6">Software Engineer</p>
          <div className="flex justify-center space-x-4">
            {socialLinks.map((social) => (
              <a
                key={social.name}
                href={social.href}
                className="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100 transition-colors duration-200"
                aria-label={social.ariaLabel}
                target={social.name !== 'Email' ? '_blank' : undefined}
                rel={social.name !== 'Email' ? 'noopener noreferrer' : undefined}
              >
                {social.icon}
              </a>
            ))}
          </div>
        </div>
      </div>
    </header>
  );
};

export default HeroSection;
