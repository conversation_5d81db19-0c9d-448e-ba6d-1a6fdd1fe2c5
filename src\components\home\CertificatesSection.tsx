import React from 'react';
import { motion } from 'framer-motion';
import awsBadge from '../../../assets/aws-badge.svg';

interface Certificate {
  title: string;
  issuer: string;
  year: string;
}

const certificates: Certificate[] = [
  {
    title: 'AWS Certified Cloud Practitioner',
    issuer: 'Amazon Web Services',
    year: '2023 - 2027'
  }
];

const CertificatesSection: React.FC = () => {
  const sectionVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.6 },
    },
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.3,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, scale: 0.8 },
    visible: {
      opacity: 1,
      scale: 1,
      transition: { duration: 0.5 },
    },
  };

  return (
    <motion.section
      variants={sectionVariants}
      initial="hidden"
      whileInView="visible"
      viewport={{ once: true, margin: "-100px" }}
      className="py-16 bg-gradient-to-b from-white via-blue-50/20 to-purple-50/30 dark:from-gray-900 dark:via-gray-900/90 dark:to-gray-800/50 transition-colors duration-300"
    >
      <div className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
        <h2 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-12 text-center">Certificates</h2>
        <motion.div
          variants={containerVariants}
          className="space-y-6"
        >
          {certificates.map((certificate, index) => (
            <motion.div
              key={index}
              variants={itemVariants}
              className="rounded-lg shadow-lg dark:shadow-gray-900/20 p-6 flex items-center transition-colors duration-300 gap-6 flex-wrap hover:shadow-xl"
              style={{
                background: 'linear-gradient(135deg, rgb(59 130 246), rgb(147 51 234))'
              }}
            >
              <div className="bg-white rounded-lg p-4 flex items-center justify-center shadow-lg">
                <img
                  src={awsBadge}
                  alt="AWS Certified Cloud Practitioner Badge"
                  className="w-16 h-16 object-contain"
                />
              </div>
              <div>
                <h3 className="text-xl font-semibold text-white mb-2">{certificate.title}</h3>
                <p className="text-blue-100">Issued by {certificate.issuer} • {certificate.year}</p>
              </div>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </motion.section>
  );
};

export default CertificatesSection;
