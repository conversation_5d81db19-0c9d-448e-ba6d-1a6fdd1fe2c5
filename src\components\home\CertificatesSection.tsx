import React from 'react';
import { motion } from 'framer-motion';
import awsBadge from '../../../assets/aws-badge.svg';

interface Certificate {
  title: string;
  issuer: string;
  year: string;
}

const certificates: Certificate[] = [
  {
    title: 'AWS Certified Cloud Practitioner',
    issuer: 'Amazon Web Services',
    year: '2023 - 2027'
  }
];

const CertificatesSection: React.FC = () => {
  const sectionVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.6 },
    },
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.3,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, scale: 0.8 },
    visible: {
      opacity: 1,
      scale: 1,
      transition: { duration: 0.5 },
    },
  };

  return (
    <motion.section
      variants={sectionVariants}
      initial="hidden"
      whileInView="visible"
      viewport={{ once: true, margin: "-100px" }}
      className="py-16 bg-white dark:bg-gray-900 transition-colors duration-300"
    >
      <div className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
        <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-12 text-center">Certificates</h2>
        <motion.div
          variants={containerVariants}
          className="space-y-6"
        >
          {certificates.map((certificate, index) => (
            <motion.div
              key={index}
              variants={itemVariants}
              className="bg-gradient-to-r from-blue-500 to-blue-600 dark:from-blue-600 dark:to-blue-700 rounded-lg shadow-lg dark:shadow-gray-900/20 p-6 flex items-center transition-colors duration-300 gap-6 flex-wrap"
            >
              <div className="bg-white rounded-lg p-4 flex items-center justify-center">
                <img
                  src={awsBadge}
                  alt="AWS Certified Cloud Practitioner Badge"
                  className="w-16 h-16 object-contain"
                />
              </div>
              <div>
                <h3 className="text-xl font-semibold text-white mb-2">{certificate.title}</h3>
                <p className="text-blue-100 dark:text-blue-200">Issued by {certificate.issuer} • {certificate.year}</p>
              </div>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </motion.section>
  );
};

export default CertificatesSection;
