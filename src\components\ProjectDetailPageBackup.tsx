import React from 'react';

export interface ProjectFeature {
  icon: React.ReactNode;
  title: string;
  description: string;
}

export interface ProjectDetail {
  title: string;
  image: {
    src: string;
    alt: string;
  };
  tags: string[];
  description: string;
  features: ProjectFeature[];
  technicalDetails: string[];
  results: string[];
}

interface ProjectDetailPageProps {
  project: ProjectDetail;
}

interface FeatureCardProps {
  icon: React.ReactNode;
  title: string;
  description: string;
}

function FeatureCard({ icon, title, description }: FeatureCardProps) {
  return (
    <div className="bg-gray-50 dark:bg-gray-700 p-4 sm:p-6 rounded-lg transition-colors duration-300 border border-gray-100 dark:border-gray-600 shadow-sm">
      <div 
        className="inline-flex p-3 rounded-lg mb-3 shadow-lg"
        style={{
          background: 'linear-gradient(135deg, rgb(59 130 246), rgb(147 51 234))'
        }}
      >
        <div className="text-white w-6 h-6">
          {icon}
        </div>
      </div>
      <h3 className="text-base sm:text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">{title}</h3>
      <p className="text-sm sm:text-base text-gray-600 dark:text-gray-300">{description}</p>
    </div>
  );
}

const ProjectDetailPage: React.FC<ProjectDetailPageProps> = ({ project }) => {
  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors duration-300">
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <article className="bg-white dark:bg-gray-800 rounded-lg shadow-lg dark:shadow-gray-900/20 overflow-hidden transition-colors duration-300">
          <div className="w-full max-h-[400px] aspect-[3/2] overflow-hidden">
            <img
              src={project.image.src}
              alt={project.image.alt}
              className="w-full h-full object-cover"
            />
          </div>
          
          <div className="p-8">
            <header className="mb-8">
              <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900 dark:text-gray-100 mb-4">{project.title}</h1>

              <div className="flex flex-wrap gap-2">
                {project.tags.map((tag) => (
                  <span 
                    key={tag}
                    className="px-2 py-1 sm:px-3 sm:py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 rounded-full text-xs sm:text-sm"
                  >
                    {tag}
                  </span>
                ))}
              </div>
            </header>

            <div className="prose max-w-none">
              <p className="text-base sm:text-lg text-gray-600 dark:text-gray-300 mb-6 sm:mb-8">
                {project.description}
              </p>

              <h2 className="text-xl sm:text-2xl font-bold text-gray-900 dark:text-gray-100 mb-4">Key Features</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 mb-6 sm:mb-8">
                {project.features.map((feature, index) => (
                  <FeatureCard
                    key={index}
                    icon={feature.icon}
                    title={feature.title}
                    description={feature.description}
                  />
                ))}
              </div>

              <h2 className="text-xl sm:text-2xl font-bold text-gray-900 dark:text-gray-100 mb-4">Technical Details</h2>
              <ul className="list-disc pl-6 text-gray-600 dark:text-gray-300 mb-6 sm:mb-8 text-sm sm:text-base">
                {project.technicalDetails.map((detail, index) => (
                  <li key={index}>{detail}</li>
                ))}
              </ul>

              <h2 className="text-xl sm:text-2xl font-bold text-gray-900 dark:text-gray-100 mb-4">Results</h2>
              <ul className="list-disc pl-6 text-gray-600 dark:text-gray-300 text-sm sm:text-base">
                {project.results.map((result, index) => (
                  <li key={index}>{result}</li>
                ))}
              </ul>
            </div>
          </div>
        </article>
      </main>
    </div>
  );
};

export default ProjectDetailPage;
