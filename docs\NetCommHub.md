# Networked Distributed System for Group-Based

# Client-Server Communication

## <PERSON><PERSON>^1 , <PERSON>^2 , <PERSON>^3 ,

## <PERSON>^4 , <PERSON><PERSON><PERSON>^5

## Group: 28

Abstract. This report presents the design and implementation of a
networked distributed system for group-based client-server commu-
nication. The system facilitates efficient group formation, message
exchange, and fault tolerance by leveraging object-oriented princi-
ples and design patterns. The report evaluates the system’s func-
tionality, performance, modularity, and fault tolerance mechanisms
through meticulous analysis and critical discussion. Key implemen-
tation choices, including adopting a graphical user interface (GUI)
and utilising the Java Swing framework, are justified based on their
contributions to usability and flexibility. The report concludes with
reflections on the project’s successes, limitations, and avenues for
future enhancements.

## 1 Introduction

This report delves into designing and implementing a networked dis-
tributed system tailored for group-based client-server communica-
tion, as outlined in the provided coursework task. It explores the
system’s intricacies, focusing on its architectural components, de-
sign choices, and adherence to established programming principles
and practices. The system architecture is centred around key ele-
ments such as clients, servers, coordinators, and message carriers,
meticulously crafted to facilitate seamless group interactions while
ensuring fault tolerance and scalability. Object-oriented design prin-
ciples and patterns, including Singleton, Command, and Factory, are
strategically employed to imbue the system with modularity, flexibil-
ity, and robustness. This report evaluates the system’s performance
in meeting core objectives through detailed analysis and critical dis-
cussion, including efficient group formation, message exchange, and
fault tolerance mechanisms. It scrutinises implementation choices,
such as utilising a graphical user interface (GUI) and adopting the
Java Swing framework, providing insights into their impact on us-
ability and system extensibility. This introduction sets the stage for
comprehensively exploring the implemented networked distributed
system. It offers a glimpse into its design intricacies, functional ca-

(^1) School of Computing and Mathematical Sciences, University of Greenwich,
London SE10 9LS, UK, email: <EMAIL>
(^2) School of Computing and Mathematical Sciences, University of Greenwich,
London SE10 9LS, UK, email: <EMAIL>
(^3) School of Computing and Mathematical Sciences, University of Greenwich,
London SE10 9LS, UK, email: <EMAIL>
(^4) School of Computing and Mathematical Sciences, University of Greenwich,
London SE10 9LS, UK, email: <EMAIL>
(^5) School of Computing and Mathematical Sciences, University of Greenwich,
London SE10 9LS, UK, email: <EMAIL>
pabilities, and avenues for further enhancement. To access the project
code, please refer to the Git repository: Azure DevOps Repo.

## 2 Design/Implementation

```
The networked distributed system is designed and implemented
around several main parts, including clients, servers, coordinators,
and message carriers. These elements work together to establish and
maintain group communication, manage message exchange, and en-
sure fault tolerance in the system. Applying object-oriented concepts
and design patterns is critical in achieving the implementation’s mod-
ularity, scalability, and robustness.
The Carrier class implements the Serializable class and is an es-
sential system component. It functions as an encapsulation object to
exchange information between the server and the clients. This class
contains the message content, sender, receiver, coordinator, authenti-
cation status, and other essential attributes. Implementing the Carrier
class and its subclasses gives the system flexibility and extensibil-
ity in handling various messages and communication scenarios. This
design choice complies with the Open-Closed Principle (OCP) [5],
which allows for simple message-type extensions without altering
the current code [2].
Furthermore, inheritance and polymorphism promote code reuse
and maintainability. For example, subclasses like ActiveCheck and
AuthRequest inherit properties and methods from the Carrier class
while adding functionality specific to their needs. This hierarchical
structure encourages code organization and ensures that messages
are handled consistently across the system.
The system’s architecture is based on a client-server model, with
clients connecting to a central server to exchange private or public
messages. The Client class encapsulates functionality such as con-
nection establishment, login process handling, and GUI interaction
management. The Client class supports bidirectional connection with
the server via sockets and input/output streams, allowing clients to
send and receive messages seamlessly. This design choice empha-
sises the Separation of Concerns principle (SoC), as the Client class
focuses on client-specific capabilities while loosely connecting to
networking implementation details [1].
Similarly, the Server class handles server-side tasks such as ac-
cepting incoming connections, transmitting messages, and keeping
the group state. The Server class utilises multiple threads to main-
tain client requests concurrently, improving system responsiveness
and scalability. Analysis of the server implementation exposes fac-
tors for performance optimisation and resource management, which
```

are critical for enabling large-scale group communication scenarios.
Additionally, the system includes fault tolerance measures to deal
with unexpected breakdowns and disruptions. For example, the Co-
ordinator class allows for automatic coordinator selection in case of
a coordinator disconnection, ensuring the group’s continuing oper-
ation and stability. Redundancy and fail-over solutions improve the
system’s reliability and resilience, reducing the impact of individual
component failures on overall system functionality.

## 2.1 UML Diagram

The following UML class diagram illustrates a system for messaging
and user interaction consisting of multiple interrelated classes. The
Server and Client classes are at the centre of the system. The server
will generate a different thread for every Client to handle the connec-
tion and the incoming communication. The server will use a UniqueI-
dGenerator component to assign IDs to each Client. The Client class,
through the LogInHandler class, will verify the correctness of the IP
and port addresses provided by the users. Furthermore, the GUI class
will provide a tool for user interaction with the system. Finally, the
RequestHandler class will take care of the communication aspect of
the Client handling all the requests and information coming from the
server. This class depends on the carrier class, a general carrier for
exchanging information. It includes attributes such as message con-
tent, sender, receiver, coordinator, authentication status, and active
member list. Several sub-classes expand the Carrier class to handle
various types of interactions. It is worth noting that the Carrier class
is the superclass for all message-related classes across the system. As
a superclass, Carrier incorporates common characteristics and meth-
ods shared by its subclasses, laying the foundation for messaging
features throughout the system. This superclass relationship ensures
that messaging logic remains consistent and reusable throughout the
program’s code. Each subclass inherits Carrier’s common character-
istics and functions, resulting in a modular and scalable design in
which specialised message types can be readily introduced and main-
tained.

```
Figure 1. UML Class Diagram
```
## 2.2 Environment Used

```
The networked distributed system was implemented using the Java
programming language, known for its resilience, platform inde-
pendence, and substantial standard library support. Java’s object-
oriented capabilities made writing modular, maintainable code eas-
ier, which is critical when designing distributed systems. The choice
of Java was also consistent with the coursework requirements,
which designated Java as the primary programming language for the
project.
In addition to development tools and programming libraries, Git
was used as a Version Control System (VCS) to manage project
changes, collaborate with team members, and trace the codebase’s
progress over time. Git’s distributed nature, branching style, and
merge features facilitated fast communication and code review
among team members. The team ensured traceability by keeping a
centralised repository on the Azure DevOps platform and leveraging
the tools to manage the software project, from planning, development
and testing.
```
## 2.3 Client Interaction Workflow

```
The user will be prompted to input the IP and port upon joining as
a new client. Once the correct data is entered, the client establishes
a connection with the server. Subsequently, a graphical user inter-
face (GUI) will be generated, granting the client access to the chat
interface. Within the chat interface, users are informed of the current
coordinator’s identity, with notifications issued for changes in co-
ordinatorship within the chat info tab. Furthermore, the client gains
access to the broadcast tab, where notifications are received for new
user arrivals and departures. Additionally, the client can broadcast
messages across the server but cannot send private messages.
```
```
(a) Coordinator GUI (b) User GUI
```
```
Figure 2. Graphic User Interface (GUI)
```
```
To enable private messaging, the client must request the details of
existing chat members. This request has been forwarded to the chat
coordinator, who will decide whether to disclose the details. Upon
acceptance, updated details appear in the chat info tab, displaying
active members and enabling private messaging by presenting their
names in a recipient menu. When a client exits the chat, any user with
their name in their recipient menu will have it removed. Conversely,
when a new client joins, the recipient menu of the current user re-
mains unchanged, and the new user does not need to request details
again.
The first client to join is automatically assigned as the system coor-
dinator. The coordinator receives updates every 5 seconds regarding
current active members, with their names displayed in a side window.
The coordinator can access the details of all users without requiring
approval. A new coordinator is assigned if the current coordinator
leaves the chat, and all users are notified accordingly.
When receiving a private message from a user whose details have
not been obtained, a new tab is generated for the private conversation.
```

To facilitate this, the details of the message sender are shared with the
recipient.

## 2.4 Utilisation of Design Pattern

The UniqueIdGenerator class provides a global access point to gener-
ate unique IDs within a specified range. Implementing the Singleton
pattern ensures that only one instance of the UniqueIdGenerator ex-
ists throughout the application, allowing easy access to the unique ID
generation capability from other classes and components without the
need to pass references or instantiate new objects [4]. This Singleton
approach provides several benefits to the overall system design. It
allows for controlled implementation of the UniqueIdGenerator in-
stance, ensuring the initialisation process is carefully managed. The
instance is created only once and appropriately configured with min-
imum and maximum values before being accessible by other com-
ponents. This leads to cleaner code, improved readability, and better
system maintainability.
The handleRequest method of the RequestHandler class displays
the Command pattern, a behavioural design pattern. Each Carrier
object sets a specific request in this pattern, allowing clients to be
parameterised with various requests. The handleRequest method de-
couples a request’s sender and receiver by accepting a Carrier object
as an argument, allowing specific actions to be executed based on the
type of request received. This technique enables flexibility and exten-
sibility in processing multiple requests because each request type is
represented by a separate object and treated individually. Overall, the
Command pattern enhances the maintainability and scalability of the
codebase by organising complex behaviours into discrete command
objects, facilitating more accessible addition of new commands and
reducing dependencies between components [3].
Furthermore, implementing the Factory Pattern in the Carrier class
offers several advantages that contribute to the overall code effective-
ness and maintainability:

- The Factory Pattern promotes code organisation and modularity
    by centralising the object creation process within a single class.
    This centralised approach simplifies adding new message types or
    modifications to existing ones, as all creation logic is confined to
    one location, enhancing code readability and reducing the risk of
    errors [6].
- The Factory Pattern facilitates the decoupling of client code from
    the specific implementations of the message sub-classes. The
    Client class only needs to interact with the createMessage fac-
    tory method, abstracting the details of object instantiation. This
    abstraction promotes flexibility and extensibility, allowing for eas-
    ier integration of new message types without modifying existing
    client code.
- The Factory Pattern promotes code reuse by encapsulating com-
    mon object creation logic.

By providing a standardised interface for object creation, the Factory
Pattern eliminates the need for repetitive instantiation code through-
out the application, leading to more concise and maintainable code.
Overall, implementing the Factory Pattern in our project enhances
code organisation, promotes flexibility and extensibility, and encour-
ages code reuse, ultimately contributing to the scalability and main-
tainability of our messaging system.

## 3 Analysis and Critical Discussion

Several observations were made on the functionality and perfor-
mance of the implemented code. The system demonstrated efficient

```
group formation, connection, and communication, allowing mem-
bers to join and communicate without errors. Messages were ex-
changed publicly and privately among group members, demonstrat-
ing the system’s ability to differentiate between types of messages.
The coordinator selection process worked adequately, automatically
selecting a replacement coordinator if the current coordinator was
disrupted or disconnected. These results demonstrate that the imple-
mented code effectively fulfils the core objectives of the coursework,
providing a solid foundation for group-based client-server communi-
cation.
In terms of modularity, fault tolerance, and testing, the system
demonstrated considerable strengths and opportunities for improve-
ment. The implementation accomplished modularity by incorporat-
ing object-oriented design concepts and design patterns such as the
Factory Method, Singleton and Command pattern. They allowed
code reuse, encapsulation, and flexibility, making the system more
maintainable and scalable. Furthermore, fault tolerance was ad-
dressed beyond the specified standards by introducing mechanisms
for aberrant terminations of members or coordinators. For exam-
ple, when the previous coordinator abruptly terminated, the system
quickly switched to a new coordinator, ensuring continuous commu-
nication among group members.
However, despite these advantages, the implementation options
have limitations and flaws that require examination. One drawback is
the need for robust error handling. The design could be implemented
by reducing complexity in classes such as the GUI Class, improv-
ing code readability and maintainability, and facilitating the testing
phase. Furthermore, design and implementation decisions could be
improved by using more design patterns and further focus on optimi-
sation to achieve better performances.
Several challenges emerged throughout the implementation, in-
cluding diagnosing network connectivity issues, ensuring concurrent
thread synchronisation, and controlling resource contention in a dis-
tributed setting. These problems were handled through testing, de-
bugging, and iterative code improvement. To address these problems,
innovative design and implementation decisions were made, such as
using asynchronous communication techniques to improve respon-
siveness and scalability.
```
## 3.1 Justification of Implementation Choices

```
Initially, we realised there were two ways to develop this project:
sending strings through scanners and printers or using objects for
communication. Sending strings represented a problem as it required
parsing and interpreting different message types, leading to type mis-
matches and increased complexity in the codebase. To overcome
these challenges, we opted for the latter approach of using objects.
By encapsulating data within objects tailored for each type of com-
munication, we ensured that each message contained precisely the
necessary information in a structured format. This approach not only
provided a scalable solution but also enhanced the maintainability
of the code. One key advantage of using objects was the scalabil-
ity it offered. As the project evolved, we could seamlessly integrate
more advanced features by introducing new types of objects to repre-
sent additional functionalities. For instance, implementing file shar-
ing, video calls, or real-time collaboration features could be achieved
by creating dedicated objects for each interaction without fundamen-
tally altering the existing code. This modular approach allowed us to
extend the system’s capabilities incrementally, ensuring it remained
flexible and adaptable to future requirements. Moreover, leveraging
objects for communication enhanced the maintainability of the code.
```

Each object encapsulated its behaviour and data, adhering to the prin-
ciples of encapsulation and abstraction. This encapsulation reduced
the complexity of individual components, making them easier to un-
derstand, debug, and modify. Additionally, the separation of concerns
facilitated by object-oriented design promoted code organisation and
modularity, simplifying maintenance tasks and reducing the likeli-
hood of unintended side effects when making changes.
We encountered another challenge when we noticed that the client
class had become increasingly intricate and challenging to maintain.
We applied the Single Responsibility Principle (SRP) to address this
issue by introducing a new class called RequestHandler.java. This
class was dedicated to handling requests for the client, focusing on
specific instances and triggering different actions accordingly. These
actions included sending messages, whether broadcast or private and
reacting to requests from the server. Furthermore, we adhered to the
Open/Closed Principle (OCP) by designing the handler to be open
for possible extensions while remaining closed to modifications. This
design approach allowed us to seamlessly accommodate new types of
requests by adding new methods to the class without altering existing
code.
In developing our messaging application, we opted for a graphical
user interface (GUI) over a command-line interface (CLI) due to its
enhanced usability and versatility. A GUI provides users with a more
intuitive and visually engaging experience, allowing more straight-
forward navigation and interaction with the application’s features. By
choosing a GUI, we aimed to create a more user-friendly interface.
Moreover, the decision to use a GUI was motivated by our desire to
facilitate potential future implementations and explore the capabili-
ties offered by the Java Swing framework. With a GUI, we have the
flexibility to incorporate various graphical elements and interactive
components, enabling us to enhance the application’s functionality
and aesthetics. Additionally, leveraging the Java Swing framework
allows us to utilise its cross-platform compatibility and extensive
components library.

## 3.2 Testing Strategy and Challenges

In our development process, we extensively utilized JUnit to test indi-
vidual classes, meticulously examining constructors and methods to
ensure their functionality. However, as we progressed towards testing
the system as a whole, we encountered challenges stemming from the
design phase. It became apparent that our system possesses a specific
workflow, and without simultaneous testing during development, cer-
tain intricacies were overlooked, leading to difficulties in testing the
system comprehensively.
We recognized the necessity of incorporating black box testing
for the main scenarios to address these challenges and mitigate po-
tential errors. By adopting this approach, we aimed to validate the
system’s behaviour against its intended functionality, considering its
workflow and overall design. This complementary testing strategy
enabled us to uncover potential issues that may have remained undis-
covered solely through unit testing with JUnit. Going forward, we
understand the importance of integrating testing seamlessly into the
development process to ensure the robustness and reliability of our
system.

## 4 Conclusion and Future Work

In conclusion, the design and implementation of the networked dis-
tributed system have yielded significant achievements in fulfilling the
coursework’s core objectives of enabling group-based client-server

```
communication. This report has illuminated the system’s strengths in
functionality, performance, modularity, and fault tolerance through
meticulous analysis and critical discussion. Object-oriented design
principles and patterns have proven instrumental in achieving code
reusability, encapsulation, and flexibility, thereby enhancing main-
tainability and scalability. The system’s ability to efficiently han-
dle group formation, message exchange, and fault tolerance mech-
anisms underscores its robustness and efficacy in real-world scenar-
ios. However, as with any complex software project, there are areas
for improvement and avenues for future work. One such area is refin-
ing error-handling mechanisms to bolster the system’s resilience to
unforeseen exceptions or disruptions. Streamlining code complexity
and further optimizing performance can also enhance the system’s ef-
ficiency and responsiveness. Furthermore, future work could involve
exploring additional design patterns and architectural refinements to
enrich the system’s robustness and extensibility. For instance, in-
corporating more advanced fault tolerance mechanisms and scala-
bility enhancements could fortify the system’s resilience to diverse
network conditions and scalability requirements. While the imple-
mented networked distributed system represents the first milestone
in achieving group-based client-server communication, it is a foun-
dation for ongoing refinement and innovation. By addressing iden-
tified limitations and embracing opportunities for enhancement, the
system can continue to evolve and adapt to the evolving needs of its
potential users and stakeholders.
```
## ACKNOWLEDGEMENTS

```
We want to sincerely thank our dedicated tutors for their unwavering
support and for making the subject educational and truly exciting.
Their passion for the topic has been infectious, propelling us into a
deeper understanding and appreciation.
```
## REFERENCES

```
[1] Bart De Win, Frank Piessens, Wouter Joosen, and Tine Verhanneman,
‘On the importance of the separation-of-concerns principle in secure
software engineering’, inWorkshop on the Application of Engineering
Principles to System Security Design, pp. 1–10. Citeseer, (2002).
[2] Konrad Grochowski, Michał Breiter, and Robert Nowak, ‘Serialization
in object-oriented programming languages’, inIntroduction to data sci-
ence and machine learning, 1–18, IntechOpen, (2019).
[3] Sagar Gupta, ‘Command pattern design for web application’, inProceed-
ings of Fifth International Congress on Information and Communica-
tion Technology: ICICT 2020, London, Volume 1, pp. 330–338. Springer,
(2021).
[4] Vijay K Kerji, ‘Abstract factory and singleton design patterns to create
decorator pattern objects in web application’,International Journal of
Advanced Information Technology (IJAIT) Vol, 1 , (2011).
[5] Robert C. Martin,Clean Architecture: A Craftsman’s Guide to Software
Structure and Design, Prentice Hall, 1 edn., September 20 2017.
[6] Zilvinas Vaira and A Caplinskas, ‘Case study towards implementation ofˇ
pure aspect-oriented factory method design pattern’,Proc. IARIA PAT-
TERNS, 11 , 102–107, (2011).
```

