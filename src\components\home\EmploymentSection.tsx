import React from 'react';
import { motion } from 'framer-motion';
import { Briefcase } from 'lucide-react';

interface JobExperience {
  title: string;
  company: string;
  period: string;
  responsibilities: string[];
}

const jobExperiences: JobExperience[] = [
  {
    title: 'Senior Software Engineer',
    company: 'TechCorp Solutions',
    period: '2021 - Present',
    responsibilities: [
      'Led development of cloud-native microservices architecture',
      'Reduced system latency by 40% through optimization',
      'Mentored junior developers and conducted code reviews'
    ]
  },
  {
    title: 'Full Stack Developer',
    company: 'InnovateTech',
    period: '2019 - 2021',
    responsibilities: [
      'Developed and maintained multiple client-facing applications',
      'Implemented CI/CD pipelines reducing deployment time by 60%',
      'Collaborated with UX team to improve user experience'
    ]
  }
];

const EmploymentSection: React.FC = () => {
  const sectionVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.6 },
    },
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.3,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, x: -50 },
    visible: {
      opacity: 1,
      x: 0,
      transition: { duration: 0.5 },
    },
  };

  return (
    <motion.section
      variants={sectionVariants}
      initial="hidden"
      whileInView="visible"
      viewport={{ once: true, margin: "-100px" }}
      className="py-16 bg-gray-50 dark:bg-gray-800 transition-colors duration-300"
    >
      <div className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
        <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-12 text-center">Employment History</h2>
        <motion.div
          variants={containerVariants}
          className="space-y-12"
        >
          {jobExperiences.map((job, index) => (
            <motion.div
              key={index}
              variants={itemVariants}
              className="relative pl-8 border-l-2 border-blue-500 dark:border-blue-400"
            >
              <div className="absolute w-4 h-4 bg-blue-500 dark:bg-blue-400 rounded-full -left-[9px] top-0"></div>
              <div className="bg-white dark:bg-gray-700 p-6 rounded-lg shadow-md dark:shadow-gray-900/20 transition-colors duration-300">
                <div className="flex items-center mb-2">
                  <Briefcase className="w-5 h-5 text-blue-600 dark:text-blue-400 mr-2" />
                  <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100">{job.title}</h3>
                </div>
                <p className="text-blue-600 dark:text-blue-400 font-medium mb-2">{job.company} • {job.period}</p>
                <ul className="list-disc list-inside text-gray-600 dark:text-gray-300 space-y-2">
                  {job.responsibilities.map((responsibility, respIndex) => (
                    <li key={respIndex}>{responsibility}</li>
                  ))}
                </ul>
              </div>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </motion.section>
  );
};

export default EmploymentSection;
