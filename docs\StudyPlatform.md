### UNIVERSITY OF G<PERSON><PERSON>WICH

### FACULTY OF ENGINEERING AND SCIENCE

### SCHOOL OF COMPUTING AND MATHEMATICAL SCIENCES

# Content Summarization and Quiz

# Generation to Support Student Studies

```
Nir Peretz
001232036
```
```
Supervisor:<PERSON><PERSON>
```
```
Word count:15,000
```
### COMP1682 Final Year Individual Project

### dissertation submitted in partial fulfilment of the requirements for the degree of

### BEng (Hons) in Software Engineering

### April 2025


## Abstract.

This project addresses the challenge of enabling university students to effectively engage in re-
call knowledge and self-evaluation by transforming existing academic content into pedagogically
aligned learning materials. The aim was to design and implement a supportive, AI-driven system
that automates the summarization of university course documents and generates multiple-choice
assessment questions. The system supports structured, self-directed learning by processing up-
loaded academic files, generating concise summaries, and producing quizzes aligned with Bloom’s
Taxonomy. Unlike adaptive learning platforms or commercial study tools, this project prior-
itizes transparency, content fidelity, and alignment with university teaching materials. The
system was built using a serverless architecture on AWS to support secure and scalable deploy-
ment. Summarisation and quiz generation was powered by the Gemini large language model
API, with structured prompts used to enforce alignment with educational goals and reduce hal-
lucination. The evaluation framework combined traditional ROUGE scores for summarisation
with G-EVAL, an LLM-based evaluation technique, to assess factual consistency, clarity, and
answerability for summaries and quizzes. Results demonstrated high system reliability, with a
100% success rate in document processing and content generation across all test cases. Provided
that the API and content extraction libraries remain stable, the system is expected to main-
tain this reliability in similar deployment environments. Summaries were evaluated across five
models, with Gemini-2.5-pro delivering the most balanced output quality. However, Gemini-
2.0-flash was selected for system integration due to its reliable performance, cost-effectiveness,
and output structure. Quizzes were generated for ten lectures, with questions mapped to cog-
nitive levels. G-EVAL enabled meaningful benchmarking without gold-standard datasets, and
the structured backend enabled end-to-end flow between ingestion, processing, and delivery.
Human-in-the-loop review and safe decoding configurations further ensured output alignment
and reduced the risk of misinformation. The project successfully met its objectives and offers a
practical contribution to educational AI development. It demonstrates the feasibility of combin-
ing structured prompt engineering, cloud-native deployment, and LLM evaluation to support
higher education. Limitations such as constrained quiz scalability and the need for user testing
were acknowledged. Future work may include retrieval-augmented generation, personalization,
and expanded assessment diversity. Overall, the project provides a replicable and pedagogically
grounded approach to content transformation for educational support.


## Preface

This project was developed as part of a larger collaborative initiative to build an integrated
educational support platform aimed at helping students manage their academic goals, revision
schedules, and learning performance. The platform was designed around four modular com-
ponents: Goal Decomposition, Adaptive Scheduling, Content Summarization and Assessment
Generation, and Focus Monitoring. Each component was developed independently by a team
member using a component-based software engineering approach, reflecting modern practices
in modular and cloud-native development. This report focuses on theContent Summariza-
tion and Assessment Generationmodule. The goal was to automate the transformation
of university-provided course materials into structured summaries and multiple-choice quizzes
to support student knowledge recall. The system was built using AWS services and Google’s
Gemini API, emphasizing transparency, fidelity to source content and educational alignment.
Although full integration with the other three components was not completed within the project
timeframe, this module was designed with future integration in mind. The architecture, inter-
face design, and workflow orchestration reflect this modular intent, ensuring that the component
can operate independently while remaining compatible with a broader cloud-based system. The
project journey was shaped by both the technical challenges of serverless deployment and the
pedagogical responsibility of building ethically sound, student-facing AI tools. It stands as a
ready-to-integrate component that aligns with the platform’s original vision of providing scal-
able, transparent, and supportive educational infrastructure.


## Contents




- Abstract.
- 1 Introduction
   - 1.1 Background
   - 1.2 Aim and Objectives
   - 1.3 Methodology
   - 1.4 Report Roadmap
- 2 Literature Review
   - 2.1 Theoretical and Empirical Foundations of Educational Summarization
      - 2.1.1 Summarization as a Cognitive and Pedagogical Tool
      - 2.1.2 Pedagogical Foundations and Learning Theories
      - 2.1.3 Relevant NLP and Summarization Techniques
      - 2.1.4 Key Projects, Tools, and Studies
      - 2.1.5 Identified Challenges and Gaps
   - 2.2 Existing Work on Automated Assessment/Quiz Generation
      - 2.2.1 Approaches, Models, and Techniques
      - 2.2.2 Ethical and Academic Concerns
   - 2.3 Analysis of Literature: Synthesizing Key Themes
   - 2.4 Gap Identification and Justification for This Project
- 3 Requirements Engineering
   - 3.1 Requirements Elicitation and Analysis
   - 3.2 Assumptions and Constraints
   - 3.3 Functional Requirements
   - 3.4 Non-Functional Requirements
- 4 Design
   - 4.1 Cloud-Native Architecture Overview
   - 4.2 Document Processing Module
   - 4.3 AI Summarization
   - 4.4 Automated Quiz Generation
   - 4.5 Learning Interface and User Experience
   - 4.6 Security and Authentication
   - 4.7 Data Flow and Integration
   - 4.8 Database Schema and Data Management
   - 4.9 System Design Evolution
- 5 Implementation
   - 5.1 AWS-Based Implementation
      - 5.1.1 Amplify Gen 2 and CI/CD Integration
      - 5.1.2 Authentication and Role Management
      - 5.1.3 Data Modeling with DynamoDB
      - 5.1.4 File Storage with S3
      - 5.1.5 Lambda Functions and Event Flow
      - 5.1.6 Frontend Integration
      - 5.1.7 Pipeline Overview
      - 5.1.8 Code Repository Outline
   - 5.2 Backend Content Processing Pipeline
      - 5.2.1 Pre and Post-Processing
      - 5.2.2 Summarization
      - 5.2.3 Quiz Generation
      - 5.2.4 Prompt Engineering Strategy
- 6 Testing.
   - 6.1 Unit Testing
   - 6.2 Integration Testing
- 7 Results
   - 7.1 Document Processing Pipeline Results
   - 7.2 Summarization Results
   - 7.3 Quiz Generation Results
   - 7.4 Conclusion
- 8 Evaluation
   - 8.1 Evaluation Criteria and Methodological Reflection
   - 8.2 Evaluation Against Project Objectives
   - 8.3 Comparative and Critical Analysis
   - 8.4 Challenges and Limitations
   - 8.5 Conclusion
- 9 Legal, Social, Ethical and Professional Issues
- 10 Conclusion and Future Work
   - 10.1 Summary of the Work
   - 10.2 Summary of Outcomes
   - 10.3 Significance and Positioning
   - 10.4 Limitations and Reflections
   - 10.5 Future Work
   - 10.6 Final Reflections
- 1 Data Model Tables and Descriptions List of Tables
- 2 AI Prompt Settings – Summarization
- 3 AI Prompt Settings – Quiz Generation
- 4 Prompt Design Framework Using Role and Zero-Shot Prompting
- 5 Document Processing Component Results
- 6 Content Summarization Component Results
- 7 Bloom’s Taxonomy Mapping of Summary Content (One Sample)
- 8 Quiz Generation Component Results
- 9 Bloom’s Taxonomy Justification of Quiz Questions (One Quiz Sample)
- 10 Summary Evaluation Rankings by Model
- 1 System Architecture Diagram List of Figures
- 2 Screenshot of Lecture Summary and Quiz Interface
- 3 User Interface Design Overview
- 4 High Level System Data Flow Diagram
- 5 Student Interaction and Data Flow
- 6 Lambda Event Chain Overview
- 7 Pipeline Workflow
- 8 Project Source Code Structure
- 9 File Processing Flow
- 10 Summary Processing Flow
- 11 Quiz Processing Flow
- 12 Prompt Engineering Workflow Diagram
- 13 Content Extraction Comparison Sample
- 14 Bloom’s Taxonomy Distribution in Corrected Summary Content
- 15 Summarization ROUGE-1 scores (Precision, Recall, F1) for each model
- 16 Summarization ROUGE-2 scores (Precision, Recall, F1) for each model
- 17 Summarization ROUGE-3 scores (Precision, Recall, F1) for each model
- 18 Summarization ROUGE-L scores (Precision, Recall, F1) for each model
- 19 Overall scores of summarization models over 50 summarizations
- 20 Distribution of Quiz Questions by Bloom’s Taxonomy Level (One Quiz Sample)
- 21 Average QA evaluation scores (relevance, answerability, correctness) per model


## 1 Introduction

Artificial Intelligence (AI) reshapes how educational content is accessed, processed, and internal-
ized. Traditional educational models have long relied on one-size-fits-all instructional delivery,
with limited scalability for personalized reinforcement or continuous formative assessment. As
the volume and complexity of university learning materials increase, students often struggle to
retain key information without structured revision or support. In parallel, academic staff face
limitations in manually generating tailored learning materials, feedback, or assessments at scale.
Early AI-based solutions focused on intelligent tutoring systems, which aimed to emulate
personalized instruction. While effective in narrow domains, such systems required significant
effort to configure and failed to scale across varied subject areas (?). The recent emergence
of large language models (LLMs) such as GPT and BERT presents an opportunity to address
these limitations through flexible, general-purpose AI. These models can generate summaries,
quizzes, and structured outputs with minimal domain-specific fine-tuning, offering a practical
route for scalable educational support (??).
Despite these advances, many AI-driven systems are built around adaptive learning path-
ways, requiring continuous user behavioral data and prolonged engagement. This project adopts
a different philosophy. Rather than attempting to personalize content dynamically, it focuses
on delivering supportive materials to enhance self-directed learning. It automates the sum-
marisation of academic content and the generation of assessments for knowledge recall. This
approach aligns more closely with pedagogical strategies such as spaced repetition and formative
self-evaluation without displacing the educator’s role or the broader curriculum structure.
The system developed in this project automates the extraction, transformation, and pre-
sentation of course materials in a concise, recall-oriented format. It is designed to support
student revision and self-assessment using university-supplied documents. The backend uses a
cloud-native architecture built on AWS and integrates pre-trained language models through a
prompt-based interface. A core focus is ensuring the reliability and pedagogical relevance of the
output through clear structure, validation mechanisms, and alignment with cognitive learning
principles.

### 1.1 Background

The educational potential of AI lies not in replacing instruction but in augmenting it. Re-
search has shown that LLMs can produce coherent, well-structured content summaries and
recall-oriented assessments when guided by structured prompting strategies. These capabilities
are particularly effective when aligned with evidence-based practices such as retrieval practice,
spaced repetition, and cognitive scaffolding (??). However, the success of these systems is not
solely determined by technical capacity. Educational value depends on integration into reliable,
transparent frameworks that reflect pedagogical goals and ethical design.
Much of the literature highlights concerns about hallucination, content drift, and the risk of
superficial or misleading outputs when using LLMs in academic settings (??). As such, systems
must be designed with safeguards that promote human validation and transparent workflows.
Prompt engineering has emerged as a critical tool for achieving such control. Recent reviews show
that properly engineered prompts can meaningfully steer model outputs, allowing educators and
developers to enforce structure, relevance, and pedagogical alignment in AI-generated content
(?).
In parallel, the deployment environment must support scalable, cost-efficient delivery. Cloud-
native, serverless platforms such as AWS Lambda and Amplify provide modular backends with-
out the complexity of infrastructure management (?). These platforms align with agile devel-
opment models and enable rapid iteration, testing, and feedback integration, which is essential
for developing educational tools under time-constrained academic projects.
This project builds on these foundations. It delivers an event-driven architecture that pro-
cesses educational documents, generates summarised content, and constructs assessment items
aligned with Bloom’s taxonomy. The system operates with clearly scoped goals: to support


knowledge recall and self-evaluation using AI while ensuring outputs are constrained, review-
able, and grounded in original material.

### 1.2 Aim and Objectives

Aim

This project aims to develop a supportive AI-driven tool that automatically summarises univer-
sity course materials and generates structured assessments to enhance student studies through
content recall and self-evaluation.

Objectives

To achieve this aim, the following core objectives were defined and delivered:

- Develop a summarisation pipeline: Create an automated process for extracting con-
    tent from university learning documents and summarising it using pre-trained language
    models and structured prompts.
- Build an assessment generation module: Implement a quiz generation engine that
    uses summarised content to create categorized multiple-choice questions to support self-
    evaluation.
- Design a cloud-native application: Construct the system using AWS Amplify, Lambda,
    DynamoDB, and related services to enable scalable, serverless processing.
- Integrate structured prompt design: Apply prompt engineering frameworks to ensure
    that model outputs remain consistent with educational goals, minimize hallucination, and
    allow for educator validation in the loop.
- Validate outputs using automated metrics: Implement evaluation processes using
    ROUGE scoring for summaries and structural checks for assessments, providing early-stage
    validation and feedback loops to guide iteration.

These objectives were derived from the initial project proposal (Appendix??) and refined during
development to better align with practical constraints, system integration requirements, and the
intended scope of the final deliverable.

### 1.3 Methodology

This project adopted a two-part approach encompassing the research process and the develop-
ment of the prototype system. The research approach aligns with principles of action research,
a method involving iterative cycles of planning, acting, observing, and reflecting, aiming to ad-
dress a practical problem within a specific context through collaborative inquiry. This approach
enabled continuous system refinement based on practical testing and feedback loops, reflecting
the interplay between design theory and empirical implementation (?).
The development process followed an Agile methodology, specifically SCRUM, to structure
and manage the engineering work. Agile’s emphasis on modularity, continuous integration,
and short feedback cycles made it well-suited for this project, which involved integrating large
language models, prompt engineering, and a serverless cloud infrastructure. Work was struc-
tured into two-week sprints using Jira to plan, track, and review development milestones. Each
sprint addressed specific deliverables, including summarization tuning, question generation logic,
validation rules, and storage schema integration. Full sprint tracking and the associated risk
assessment process are provided in Appendix [??]. This structure ensured steady progress and
allowed for responsive adaptation during testing phases, consistent with Agile–cloud synergy
findings in educational software development (?).


In parallel with development, the project engaged in an extensive literature review to ground
key technical and pedagogical decisions. Research into prompt engineering frameworks informed
the structured design of input templates to guide summarisation and assessment generation (?).
These techniques were essential to reduce hallucinations, enforce content fidelity, and align the
outputs with Bloom’s taxonomy for cognitive learning levels.
The system architecture was built on cloud infrastructure using Amazon Web Services
(AWS). This decision was justified by the need for scalability, modularity, and availability,
properties essential for deploying educational tools that may require asynchronous document
processing, event-driven interaction, and low operational overhead. As serverless computing lit-
erature describes, platforms like AWS Lambda and DynamoDB enable cost-effective and highly
available backend services, particularly suited to modular NLP pipelines and high-volume con-
tent ingestion (?).

### 1.4 Report Roadmap

The remainder of this report follows the design and development trajectory of the project. The
next chapter presents a literature review that situates this work within the fields of educational
AI, content summarisation, and automated assessment generation. It critically evaluates existing
tools, models, and approaches to highlight the research gap this project addresses.
Following that, the design chapter outlines the system architecture. It discusses component
structure, service integration, data flow, and design decisions that shaped the implementation.
An implementation chapter follows this, translating the design into practical modules, services,
and prompt workflows.
After implementation, the testing chapter details the methods used to verify system func-
tionality and content alignment. This is followed by the results chapter, which presents raw
performance data, summarisation scores, and quiz generation metrics. The evaluation chapter
then critically analyzes these findings, reflecting on system strengths, educational value, and
observed limitations. The report concludes with a summary of lessons learned and outlines
potential directions for future development.


## 2 Literature Review

### 2.1 Theoretical and Empirical Foundations of Educational Summarization

#### 2.1.1 Summarization as a Cognitive and Pedagogical Tool

The use of content summarization in educational settings is increasingly recognized not merely as
a convenience but as a pedagogically grounded intervention. A growing body of empirical work
demonstrates that well-structured summaries enhance student engagement, reduce cognitive
load, and improve long-term knowledge retention. As lectures and learning materials become
denser, summarization acts as a crucial filter, enabling students to concentrate on core ideas
while organizing knowledge hierarchically.
Recent work by? introduced a theme-based summarization system using large language
models (LLMs) to generate structured lecture summaries. Their mixed-methods study found
statistically significant gains in knowledge recall among undergraduates exposed to AI-generated
summaries, regardless of whether the lecture was conceptual or procedural in nature. Students
also reported higher satisfaction levels, attributed to the increased accessibility of lecture content
and the summaries’ role in bridging knowledge across sessions. The authors attribute this to
the summaries’ ability to scaffold learning, organize content thematically, and alleviate the
extraneous cognitive load typically associated with reviewing full lecture content.
Similarly,? experimented with graduate health informatics students using BERT-based
summaries of academic papers. Peer evaluations revealed no significant difference between
student-generated and AI-generated annotations, suggesting that AI summaries can approxi-
mate expert-level output. Students noted that such summaries helped them process research
articles more efficiently and identify key concepts, thereby making expert-level content more
approachable and cognitively manageable. Their findings align with prior cognitive research,
highlighting that summarization supports the encoding of content into long-term memory by
structuring complex input into digestible segments.
Both studies reinforce the broader claim that summarization is not simply a content reduction
technique but a means of supporting metacognitive processing, aiding in review and reflection,
and enabling self-directed learning. These benefits are especially pronounced in higher educa-
tion, where time constraints, volume of material, and varying levels of student preparedness
create a strong demand for intelligent scaffolding tools. Consequently, content summarization is
positioned in this project, not as an ancillary feature but as a core pedagogical mechanism for
supporting knowledge recall.

#### 2.1.2 Pedagogical Foundations and Learning Theories

Educational technologies such as summarization and assessment generation cannot be mean-
ingfully evaluated without considering the pedagogical frameworks they aim to support. This
project draws from established learning theories, particularly constructivism, Bloom’s taxonomy,
and evidence-based cognitive strategies, to ensure alignment between technical implementation
and learning goals.
Social constructivist models, emphasize that learning is a socially situated process where
knowledge is co-constructed through interaction, reflection, and scaffolding (?). In this model,
learners are active participants, and tools such as AI summarization and quiz platforms serve
not as substitutes for instruction but as facilitators of student-driven inquiry and reflection.
Techniques such as peer scaffolding, reciprocal teaching, and inquiry-based problem-solving are
consistent with how students engage with system-generated summaries and assessments. These
technologies provide cognitive scaffolds that promote development beyond the learner’s current
level of understanding, supporting meaningful interaction with content.
Bloom’s taxonomy complements this approach by offering a structured hierarchy for evalu-
ating cognitive engagement, from basic recall to complex critical thinking (?). Summarization
systems informed by this taxonomy can prioritize different abstraction levels based on the de-
sired learning outcome. For example, summaries aimed at recall may highlight factual content,


whereas those targeting higher-order thinking may emphasize conceptual relationships or causal
links. This framework also informs prompt design choices, enabling alignment between the
cognitive demand of the summarized content and its instructional purpose.
In addition to these pedagogical models, spaced repetition is integrated as a cognitive strat-
egy for improving long-term retention. Unlike massed learning or rereading, spaced repetition
strengthens memory through systematic and timed material reviews. Empirical studies confirm
its value in enhancing both factual recall and conceptual understanding (?). By linking sum-
marization and assessment outputs to a repetition cycle, this project integrates neuroscientific
principles into its content delivery, helping students retain information more effectively over
time.
Collectively, these frameworks ensure that the system is technically sound and grounded in
established educational theory. AI-generated content is designed to support structured progres-
sion, cognitive engagement, and retention, resulting in pedagogically purposeful outputs and
support of student knowledge recall.

#### 2.1.3 Relevant NLP and Summarization Techniques

The rapid development of transformer-based LLMs, including BERT, GPT, and T5, has ex-
panded the possibilities for content summarization in education. These models are central to
modern summarization workflows due to their ability to adapt across subject domains with
minimal task-specific training (?). Their use in educational contexts is not only a matter of
efficiency but also of pedagogical alignment, as their capabilities can be tuned to reflect different
instructional objectives.
Summarization techniques are typically divided into extractive and abstractive methods.
Extractive methods select exact phrases or sentences from the source text, preserving factual
accuracy and semantic fidelity. This makes them well-suited for academic domains where pre-
serving original meaning is essential (?). Abstractive summarization, by contrast, paraphrases
and synthesizes information to produce more fluent, human-like output. While this increases
readability and engagement, it also introduces the risk of hallucination or distortion of meaning
(??). These trade-offs are critical in educational applications, where both clarity and correctness
must be maintained.
While the literature often advocates hybrid approaches to balance factual integrity and flu-
ency (??), this project adopts an abstractive-only approach, emphasizing controlled generation
over extraction. The decision is guided by the need to produce summaries that synthesize mate-
rial into higher-level concepts rather than simply surface-level condensation. To address concerns
around hallucination and faithfulness, structured prompting and output constraints are applied.
These measures aim to produce content that remains aligned with the original material while
improving readability and pedagogical value. The result is a summarization pipeline that favors
conceptual abstraction without sacrificing instructional reliability.
Prompt engineering has emerged as a decisive factor in leveraging LLMs for educational NLP
tasks, including summarization and assessment generation. Techniques such as zero-shot, few-
shot, and role-based prompting offer pathways to shape model outputs without fine-tuning, but
their effectiveness hinges on precise design, task-context alignment, and pedagogical relevance.
Recent systematic reviews emphasize that prompt engineering is more than a functional
interface; it is a design space that governs task performance, coherence, and pedagogical fidelity
(?). For instance, zero-shot prompting remains widely used due to its simplicity, but few-shot
methods, which include curated exemplars, often outperform it in generating structured and
valid outputs (?).
Further advancements explore role-based prompting, wherein LLMs are framed to simulate
specific instructional roles (e.g., teacher, examiner). While this approach has gained traction
to guide model behavior in classroom-like contexts, its effectiveness remains context-dependent.
Evidence from recent large-scale evaluations suggests that persona-based prompting does not
reliably improve model accuracy on objective tasks (?). These findings caution against assum-
ing that anthropomorphic prompts inherently boost educational outcomes. Instead, prompt


effectiveness is better predicted by domain alignment and structured guidance than by social
framing alone.
Despite mixed results, role-play prompting shows promise as an implicit mechanism for
triggering structured reasoning in models.? demonstrate that assigning a task-specific role,
such as “math teacher,” can boost zero-shot reasoning accuracy by implicitly triggering chain-
of-thought responses. This effect, while model-dependent, offers a useful design strategy for
scenarios that require explanation, coherence, or reflection, such as summarization. However,
the benefits are not uniform across domains, and identifying the most effective roles remains a
challenge without manual curation or dynamic prompt adaptation.
In summary, while prompt engineering is not a turnkey solution, it provides a flexible and
powerful abstraction layer for guiding model behavior. Designing prompts for educational pur-
poses requires careful consideration of the task, learner needs, and the trade-offs between gen-
erality and specificity. This project draws on these insights by adopting structured zero-shot
and role-play prompting protocols grounded in pedagogical goals rather than user simulation to
ensure output relevance and reliability.

#### 2.1.4 Key Projects, Tools, and Studies

Efforts to incorporate NLP-based summarization into educational tools remain limited in scale
and integration. Commercial platforms such as Kinnu offer topic-driven summaries aimed at
enhancing long-term retention through structured learning flows. While pedagogically aligned,
Kinnu’s methodology is opaque, and its summarization pipeline lacks transparency regarding
fidelity, validation, and feedback mechanisms (?).
Academic prototypes provide more detail but often remain proof-of-concept.?introduce a
system combining summarization and quiz generation. Though promising in scope, the system
lacks institutional validation and does not include oversight tools for educators. Similarly,?
proposes a dual-phase architecture leveraging BERT and GPT, targeting concept extraction
followed by generative summarization. Despite improved coherence and topic alignment, their
approach remains confined to narrow domains and does not scale across content types.
Recent work by?and?focuses on improving summarization accuracy. The former explores
techniques to mitigate hallucinations in LLM-generated content using controlled prompts, while
the latter examines extractive summarization via ChatGPT, prioritizing faithfulness and align-
ment with the source material. These studies provide technical advances but do not evaluate
educational integration.
The survey by? presents a comprehensive overview of prompt engineering strategies in
NLP, highlighting techniques such as prompt tuning and hybrid prompting as key determi-
nants of output quality in LLM-based tasks. While their focus is not limited to education, the
findings underscore methodological variables influencing summarization reliability. Although
various studies demonstrate the domain-specific adaptability of LLMs for summarizing scientific
or educational abstracts, few directly address their integration into student-facing workflows.
Finally,? explore AI ethics and content trustworthiness, which, while not summarization-
specific, are critical when applying summarization in high-stakes educational settings. Their
review reinforces the importance of GDPR compliance, transparency, and educator oversight.
In summary, while summarization models have evolved technically, their deployment in real-
world educational platforms remains shallow. Most systems optimize linguistic quality or content
fidelity but overlook curriculum alignment, educator involvement, and pedagogical evaluation
factors this project explicitly addresses.

#### 2.1.5 Identified Challenges and Gaps

Despite increasing attention toward educational summarization systems, several recurring chal-
lenges remain unresolved, undermining their scalability and pedagogical reliability. Chief among
these is the issue of factual inaccuracy, particularly in abstractive models. As? noted, these
models often generate fluent yet incorrect content, a phenomenon commonly referred to as


”hallucination.” This presents significant concerns in academic settings where factual fidelity is
non-negotiable.
?and?point to another persistent trade-off: models prioritizing coherence and fluency often
sacrifice faithfulness to the source material. These trade-offs become especially pronounced in
hybrid pipelines attempting to combine extractive and abstractive stages. Even layered systems
require careful, prompt calibration and output filtering, increasing implementation complexity
and oversight needs.
Large-scale evaluations, such as those by?, further reveal performance instability across do-
mains and input lengths. Their comparative study of summarization models, including propri-
etary and open-weight alternatives, confirms variability in ROUGE and BERT scores depending
on dataset structure and prompt design. While some models outperform others in controlled
settings, their effectiveness remains highly dependent on prompt quality and context-aware sam-
pling.
Another underexplored area is alignment with learning outcomes. Many systems, especially
commercial or open-source prototypes, lack integration with instructional design frameworks.
As a result, the generated summaries may omit pedagogically critical material or emphasize
non-essential content. This limitation is further aggravated in environments where educators
cannot intervene or validate outputs in real-time.
Finally, despite the clear role of prompt engineering in improving LLM performance, few
summarization systems incorporate dynamic or feedback-driven prompting strategies.?argue
that rigid prompt templates perform poorly, mainly when applied to diverse or multi-topic course
materials. This restricts reuse and generalisability across different academic contexts.
In conclusion, while summarization technologies have evolved rapidly, current solutions fall
short in addressing factual integrity, pedagogical alignment, and adaptive prompting. These
deficiencies highlight a critical gap that this project aims to address through extractive-first
pipelines, robust prompt strategies, and educator-in-the-loop validation mechanisms.

### 2.2 Existing Work on Automated Assessment/Quiz Generation

#### 2.2.1 Approaches, Models, and Techniques

Automated assessment generation has progressed significantly with the rise of neural language
models and prompt-based learning. Early approaches were largely rule-based, relying on prede-
fined templates or surface-level NLP features to generate questions, typically multiple-choice or
fill-in-the-blank. These approaches suffered from brittleness and low linguistic variability (?).
Neural models such as T5 and GPT have enabled more nuanced and context-aware question
generation, especially with prompt engineering. For example,? demonstrates that zero-shot
prompting strategies can generate grammatically coherent and semantically aligned questions
without requiring task-specific training data. Similarly,? shows that well-structured auto-
generated quizzes can improve engagement and performance, particularly when combined with
targeted feedback loops.
The PDFQuiz platform illustrates how these capabilities are being commercialized. It trans-
forms static PDFs into multiple-choice quizzes using model-backed NLP pipelines. However, the
platform’s design details are not publicly disclosed, making it difficult to assess its pedagogical
robustness or compatibility with instructional frameworks such as Bloom’s taxonomy (?).
Recent academic proposals offer more pedagogically grounded designs. The system described
by? introduces a teacher-facing quiz design interface where educators refine AI-generated
suggestions. This hybrid workflow promotes high-quality question formation and balances au-
tomation with instructional oversight. Similarly,? presents a pipeline that integrates content
summarisation and question generation using keyword extraction, prompt-based GPT summari-
sation, and question formulation. Though promising in design, the system targets engagement
rather than curriculum fidelity and is not evaluated across diverse subjects.
These systems underscore key themes: prompt design, content alignment, and educator in-
tegration. The move from fixed templates to generative models has improved linguistic diversity


and relevance. However, persistent challenges remain to ensure question appropriateness, avoid
trivial outputs, and align assessments with learning outcomes. These gaps shape the technical
and pedagogical design of the current project’s assessment module.

#### 2.2.2 Ethical and Academic Concerns

Integrating generative AI into assessment workflows raises several critical ethical and academic
concerns. One central issue is the potential for inaccurate, biased, or misleading questions,
mainly when using LLms without sufficient human oversight.? highlights the importance of
perceived usefulness and ease of use in students’ acceptance of AI-driven e-learning platforms.
Therefore, inconsistencies in AI-generated content can diminish these perceptions, potentially
leading to decreased user satisfaction and a reduced intention to use such platforms.
Another frequently raised concern is academic integrity.? and? note that students may
attempt to game or exploit AI-generated assessments, especially when questions are overly
simplistic or repetitive. These risks are compounded when systems lack mechanisms for question
diversity, difficulty calibration, or alignment with intended learning outcomes.
Bias in assessment content also remains a substantial issue. Generative models trained on
large internet corpora may unintentionally reflect cultural, gender, or disciplinary biases in their
phrasing or framing of questions (?). Without explicit filtering or educator review, such biases
could lead to unfair evaluations or reinforce existing inequalities in learning environments.
Additionally, there are significant concerns about data privacy and regulatory compliance.
The use of student data for model fine-tuning or personalized content delivery must adhere
to strict privacy standards, such as GDPR. As highlighted by?, AI platforms must clearly
define data processing practices and provide auditability to avoid breaching institutional or
legal safeguards.
While some tools aim to mitigate these issues through user-in-the-loop design, most remain
technically driven and pedagogically shallow. The systematic review by? argues that ethical
implementation requires ongoing teacher training, participatory design processes, and trans-
parent algorithmic accountability. These recommendations support this project’s architecture,
prioritizing human validation of AI-generated assessments and avoiding reliance on opaque or
unsupervised automation.
In conclusion, AI-enabled assessment systems offer substantial efficiencies but must be im-
plemented with deliberate ethical safeguards. Issues of bias, integrity, transparency, and data
protection are not peripheral; they are foundational design constraints that shape such tech-
nologies’ trustworthiness and educational value.


### 2.3 Analysis of Literature: Synthesizing Key Themes

across the reviewed literature, several common themes emerge regarding the use of generative
AI in education. While significant progress has been made in summarization and quiz gener-
ation, few systems offer a unified pipeline that integrates both tasks into a cohesive learning
experience. Summarization systems often stop at content condensation, and assessment tools
rarely contextualize their questions based on summaries or intended learning outcomes (??).
Another recurring observation is the lack of educator oversight and pedagogical ground-
ing. Many implementations, especially commercial platforms, offer little transparency into how
summaries or questions are generated, evaluated, or aligned with curriculum goals (??). Aca-
demic proposals frequently emphasize technical performance but lack validation studies involving
teachers or students.
Prompt engineering, while shown to improve performance in summarization and quiz gen-
eration tasks??, is often underused or static. Few systems employ feedback loops or adaptive
prompting strategies that respond to content complexity, domain specificity, or student per-
formance. This gap limits the generalizability of current solutions across varied educational
contexts.
Further, ethical and regulatory considerations are frequently acknowledged but insufficiently
embedded in system design. Concerns such as hallucination, bias, GDPR compliance, and aca-
demic integrity are noted across multiple studies (??), but implementation practices to address
them remain ad hoc. Systems often defer responsibility to downstream human users without
offering robust built-in controls.
Finally, very few studies explore how such AI systems should be deployed at scale. There is a
lack of focus on deployment architecture, versioning, data governance, and cloud-native integra-
tion. While some papers mention LLM APIs, none address end-to-end workflows incorporating
document processing, model orchestration, validation, and student delivery using scalable cloud
platforms.
These observations underscore a structural fragmentation in current research. Most solu-
tions address a single element of the learning process, often in isolation. The literature lacks a
systems-level perspective that connects summarization, assessment, ethical oversight, and scal-
able deployment into a unified educational toolchain, precisely the integration this project seeks
to provide.

### 2.4 Gap Identification and Justification for This Project

The literature reveals a clear fragmentation in existing solutions for AI in education. Tools either
focus on summarization or assessment generation, rarely both. Where integration is attempted,
it lacks validation workflows, pedagogical alignment, and educator oversight. This project re-
sponds directly to these gaps through a consolidated platform that automates summarization
and quiz generation, validated by human reviewers.
Unlike commercial tools that conceal system logic or academic studies emphasizing narrow
proofs-of-concept, this project prioritizes educational transparency. Summaries are generated
using abstractive techniques guided by structured prompts and output constraints to balance
coherence and fidelity, addressing well-documented concerns around hallucination (?). Assess-
ments are generated through structured prompting informed by pedagogical constraints and
filtered using an educator-in-the-loop process to ensure appropriateness and diversity (?).
The project also addresses prompt engineering gaps identified in the literature. While most
reviewed systems rely on static templates or general-purpose instructions, this platform uses
structured zero-shot prompting and role-based formulations to elicit concise, context-aware out-
puts. This aligns with recommendations from?,?, and? regarding the importance of well-
crafted prompts for content fidelity and task alignment.
In contrast to proposals that leave deployment concerns unaddressed, this system is imple-
mented using AWS serverless components, ensuring security, scalability, and modular orchestra-
tion. This cloud-native approach supports future extensibility and aligns with best practices in


educational technology architecture.
Finally, ethical and legal concerns are directly embedded in the project’s design. Drawing
on recommendations by? and?, the platform avoids storing sensitive user data, incorporates
GDPR-aware configurations, and positions educator validation as a mandatory review step for
all AI outputs.
In summary, this project delivers a targeted and novel response to the documented short-
comings in current educational AI systems, bridging summarization and quiz generation through
transparent workflows, scalable deployment, and principled oversight.


## 3 Requirements Engineering

### 3.1 Requirements Elicitation and Analysis

The system requirements were derived using multiple complementary methods:

- Review of Existing Products: Analysis of existing content summarization and quiz
    generation tools revealed limitations in AI-powered educational content processing and
    the need for automated assessment generation.
- Review of Academic Literature: Research on natural language processing, content
    summarization, and automated question generation informed the core AI features and
    processing approaches.
- Educational Technology Trends: Investigation of modern e-learning platforms high-
    lighted the growing demand for AI-assisted content processing and assessment tools.
- Technical Framework Evaluation: The selection of AWS Amplify, React, and server-
    less architecture was driven by scalability requirements and modern cloud-native develop-
    ment practices.

### 3.2 Assumptions and Constraints

Assumptions

- Admin users are educators and educational content creators who need to process lecture
    materials.
- Admin users will provide lecture notes material following standard academic formats.
- AWS infrastructure provides sufficient processing power for content analysis and AI oper-
    ations.
- Users have already attended or watched the lecture material before watching the summary.
- Users are aware that the summaries and assessments are generated by AI and are expected
    to apply critical thinking when engaging with the content.

Constraints

- The development timeline is limited to the duration of the academic project.
- Processing was restricted to PDF and PowerPoint formats initially.
- AI processing dependent on external API limitations (Gemini 2.0 Flash - Free Version).
- The processing queue is limited to one document at a time per user.

### 3.3 Functional Requirements

Must-Have

- Upload and process PDF and PowerPoint documents.
- Extract and summarize content using AI.
- Generate multiple-choice questions automatically from processed content.
- Identify and extract key concepts from uploaded documents.
- Preserve the original document’s context during processing.


- Authenticate users securely and enforce role-based access control.

Should-Have

- Provide an administrative review interface for processed content.
- Categorize generated questions by topic and difficulty level.
- Provide feedback on individual student progress.
- Provide answer explanations for generated questions.

Could-Have

- Allow customization of lecture and quiz difficulty.
- Support batch processing of multiple documents.
- Offer advanced search functionality across uploaded content.

Will-Not-Have

- Provide dedicated native mobile applications for iOS and Android.
- Enhance summaries with embedded visuals such as images, charts, or video links.
- Allow admin users to export summaries, questions, and results to common formats.
- Enable integration with LMS platforms such as Moodle for content import/export and
    student progress sync.

### 3.4 Non-Functional Requirements

Must-Have

- Secure authentication and role-based access control using AWS Cognito.
- Encrypted data storage and transmission (HTTPS, S3 encryption).
- Serverless architecture using AWS Lambda and DynamoDB for scalability.
- Document processing completion within 1 minute (including AI summary processing).
- Modular system architecture supporting maintainability and testing.

Should-Have

- Responsive UI across desktop and mobile devices.
- Clear documentation for development and maintenance.
- Performance monitoring and basic error tracking.
- Graceful error handling and recovery mechanisms.

Could-Have

- Enhanced logging and diagnostics for debugging.
- Automated code quality tools.

Will-Not-Have

- Offline functionality.
- Enterprise-level monitoring dashboards.


## 4 Design

### 4.1 Cloud-Native Architecture Overview

The system adopts a cloud-native serverless architecture that maximizes scalability and main-
tainability. All backend logic is implemented as AWS Lambda functions and orchestrated in an
event-driven manner. This modular design decouples components, allowing independent scal-
ing and failure isolation (?). For example, uploading a document triggers an S3 event that
invokes a Lambda for processing, and content approval triggers downstream Lambdas for AI
processing. AWS Amplify is an infrastructure-as-code toolkit to configure cloud resources (API,
storage, auth) and seamlessly deploy the React frontend. The frontend is built with React and
Tailwind CSS, ensuring a fast, dynamic user experience. It communicates with the backend
through AWS AppSync (GraphQL API), which provides efficient data queries and real-time
updates. The architecture emphasizes loose coupling between services: the frontend interacts
only via the API, and backend functions communicate through events and database streams.
This yields a highly scalable platform; each function and service can scale automatically based
on load, and no servers are managed directly by developers (?). Using fully managed cloud
services also enhances reliability and security by offloading infrastructure management to AWS.
In summary, the design aligns with modern cloud architecture best practices: it is event-driven,
serverless, and microservices-oriented, which improves agility, scalability, and cost-effectiveness
for an educational content system (?).

```
Lambda Functions
```
```
HTTPS
```
```
Web Client
React App
```
```
AWS Cloud
```
```
Dynamo DB
```
```
AWS AmplifyAmplify
```
```
Quiz User Progress
```
```
Course
```
```
QuizQuestion
```
```
Authenticates
```
```
Queries/MutationsGraphQL
```
```
Frontend Hosting
```
```
CRUD Operations
```
```
API Layer
AppSync
Authorization
```
```
Authentication
Cognito
```
```
Lecture
```
```
Storage Invokes
```
```
Lectures
```
```
FileProcessor DataProcessor Summarization Quiz-Generator
```
```
Invoke
IAM Roles
```
```
CRUD Operations
```
```
IAM
```
```
Figure 1: System Architecture Diagram
```

### 4.2 Document Processing Module

Document processing is the first stage of the system’s pipeline, responsible for ingesting raw
lecture materials (PDF slides, PowerPoint files) and extracting usable text content. When
uploaded, a document is stored in S3 and triggers a Lambda function to extract and preprocess
its content. Only after administrator approval does it proceed to AI summarization. Storing
raw extracted content before AI summarization allows a human in the loop: administrators can
review and edit the content for accuracy or relevance. This two-step approach (extraction then
approval) reflects a content management best practice, ensuring that AI operates on vetted data
and thus upholds content quality. The document processing module is designed to be extensible

- new file types can be supported by adding extractor plugins – and robust, with error handling
that catches extraction failures and notifies administrators. By automating the tedious task
of transcribing lecture files, this module saves educators time and forms the foundation for
subsequent AI-driven summarization.

### 4.3 AI Summarization

Once the content is approved, the system leverages Gemini AI, an LLM by Google, to generate
a concise, structured summary of the lecture material. The design of the summarization module
uses a Lambda function triggered by a DynamoDB stream event when a lecture’s status changes
to ”approved.” This Lambda composes a prompt encapsulating the lecture text and specific
instructions for educational summarization. It then calls the Gemini AI API to produce an
abstractive summary in Markdown format. Abstractive LLM summarization is state-of-the-
art, as recent research trends have shifted from purely extractive methods toward abstractive
techniques for more coherent and human-like summaries (?). The advantages for learning are
significant: studies show that AI-generated summaries can improve students’ recall of key ideas
and overall satisfaction. For instance,? found that using an LLM to summarize lectures
improved knowledge retention and student satisfaction with the learning material. The summary
includes structured sections that are aligned with pedagogy (Bloom’s taxonomy). Each summary
is tagged with a difficulty level and an estimated reading time based on that level, supporting
better planning and self-paced learning. The generated summary is stored back into the lecture
record in DynamoDB. By integrating Gemini AI, the system automatically summarizes text
at scale, converting lengthy lecture notes into manageable highlights. Overall, this module
demonstrates how generative AI can augment educational content creation, providing students
with digestible summaries. It aligns with emerging positive perceptions of AI in education;
surveys indicate students appreciate AI tools for personalized learning support and content
explanation (provided concerns like accuracy are managed) (?).

### 4.4 Automated Quiz Generation

After summarization, the system automatically creates assessment questions to reinforce learn-
ing. The quiz generation module uses the Gemini AI to produce a set of multiple-choice questions
(MCQs) based on the lecture content and summary. This is implemented via a Lambda function
that is invoked upon successful completion of the summarization Lambda (chained invocation).
Using carefully crafted prompts, it requests the AI to generate a series of quiz questions, each
with four answer options, a correct answer identification, and an explanation. Each question
is categorized by topic and difficulty level, enabling more targeted revision and analysis of
performance. The questions are designed to cover the key points of the lecture, serving as a
form of retrieval practice.? supported this design choice, which found that retrieval practice
(quizzing) led to significantly better long-term retention of factual knowledge than passive review
or summarization alone. By automatically generating quizzes, the system ensures that such
retrieval practice is immediately available for each piece of content. Recent work in AI-driven
question generation further validates this approach. LLM-based question generators have been
shown to produce high-quality, relevant questions and are perceived positively by educators. A
user study demonstrated that an automated lecture quiz system using BART generated clear


and relevant questions, indicating potential as a valuable educational tool (?). The system
stores each generated question in a database table (with references to the lecture) and groups
them into a Quiz entity. An admin interface allows instructors to review the AI-generated
questions, ensuring a human quality check. The design thus combines the efficiency of AI with
educator oversight for quality. Overall, this module provides on-demand formative assessments,
reinforcing learning objectives and aligning with the system’s goal of an end-to-end learning
cycle.

### 4.5 Learning Interface and User Experience

The platform provides a unified interface that supports structured learning and seamless inter-
action. Built with React and styled using Tailwind CSS, the frontend presents AI-generated
summaries and quizzes in a responsive layout optimized for desktop and mobile devices. Stu-
dents interact with course materials through a streamlined dashboard that organizes content
into courses, lectures, and quizzes. Summaries are rendered from Markdown, preserving logical
structure and formatting. After reading a summary, students complete a quiz one question at
a time, receiving immediate feedback and explanatory answers. Each quiz attempt updates the
user’s progress, which is visualized in a personal dashboard showing completed lectures, scores,
and retake options.

```
Figure 2: Screenshot of Lecture Summary and Quiz Interface
```
To enhance usability, the interface follows user-centered design principles. Straightforward nav-
igation, real-time visual responses (e.g., color-coded answers, upload confirmations), and consis-
tent layouts reduce cognitive load and guide the user journey. Accessibility and responsiveness
were prioritized throughout the design to ensure a smooth learning experience across screen sizes
and input modes.

```
Figure 3: User Interface Design Overview
```
Further interaction logic, rendering behavior, and accessibility considerations are detailed in
Appendix [??].


### 4.6 Security and Authentication

Security is built into the platform through a layered model combining authentication, authoriza-
tion, and secure data handling. User identity is managed via Amazon Cognito, which supports
email-based login, role-based access, and multi-factor authentication for administrators. Access
is controlled through role-based authorization rules defined in Cognito user groups and AWS
AppSync’s GraphQL schema. Admins can upload and approve content, while students are lim-
ited to viewing the learning data. All interactions are authorized using JSON Web Tokens
(JWTs), ensuring secure communication with backend services. Sensitive data is encrypted
at rest (in S3 and DynamoDB) and in transit via HTTPS. Gemini API credentials are stored
securely in AWS Secrets Manager and injected into Lambda functions at runtime.
This approach aligns with AWS security best practices, ensuring the confidentiality, integrity,
and availability of data. For implementation-level IAM configuration, field-level authorization,
and runtime security enforcement, see Appendix [??].

### 4.7 Data Flow and Integration

The platform uses an event-driven architecture to coordinate frontend actions with asynchronous
backend processing. When an administrator uploads a document, a multi-stage pipeline is
triggered: file storage in S3, content extraction, AI summarization, and quiz generation. These
stages are orchestrated using AWS-native services such as S3 event notifications, DynamoDB
Streams, and chained Lambda invocations. Figure [4] provides a high-level overview of this
architecture, illustrating the roles of educators, students, and system components.

```
User
```
```
Selects Course Takes Quiz Views Results and Progress
```
```
Database
```
```
Stores and
Provides Summaries & Quizzes
```
```
Content Summarization and Assessment Generation System
```
```
Processes Materials Generates Summaries and Quizzes Grades Performance and Updates Progress
```
```
Educator / Admin
```
```
Uploads Course Content
```
```
Uploads Content
```
```
Processes and Saves Provides Data
```
```
Selects Course
```
```
Delivers Summaries and Quizzes
Takes Quiz and Views Results
```
```
Figure 4: High Level System Data Flow Diagram
```
From the student’s perspective, interaction begins by navigating to a course or lecture. The
frontend, built with React and AWS Amplify, uses GraphQL queries to retrieve the latest
approved summaries and quizzes. Content is fetched dynamically upon navigation, ensuring
efficiency without relying on real-time subscriptions. The flow of this interaction from data
fetching to quiz submission and progress tracking is depicted in Figure [5].


```
Student
Frontend (React +
Amplify)
```
```
AppSync
(GraphQL API) DynamoDB
Navigate to Lecture/Course
GraphQL Query (Lecture, Summary, Quiz)
Fetch lecture, summary, quiz
Return data
Return data
Display content and quiz
Answer quiz
Immediate feedback (client-side)
Submit quiz
GraphQL Mutation (UserProgress update)
Update score and completion flag
```
```
Figure 5: Student Interaction and Data Flow
```
Each content processing stage on the backend is handled by a dedicated Lambda function with
scoped responsibilities. These functions operate independently and are invoked by event triggers,
forming a loosely coupled, resilient pipeline. Figure [6] shows the sequential execution path from
document ingestion to quiz generation.

```
AWS Lambda
```
```
File Processing
```
```
AWS Lambda
```
```
Data Processing
```
```
AWS Lambda
```
```
Quiz Generator
```
```
AWS Lambda
```
```
Human in the Loop Summarization
```
```
Figure 6: Lambda Event Chain Overview
```
This architecture supports automation, scalability, and modularity, enabling the platform to
process and deliver educational content efficiently and reliably. For a technical breakdown of
Lambda configurations, GraphQL mutation handling, and IAM separation, see Appendix [??].


### 4.8 Database Schema and Data Management

The system uses Amazon DynamoDB as the primary data store due to its scalability, low-latency
performance, and native integration with AWS serverless services. The schema is designed to
support core entities such as courses, lectures, quizzes, questions, and user progress. Each table
is structured to optimize common access patterns while enabling future extensibility.

```
Table 1: Data Model Tables and Descriptions
Table Key Attributes Purpose / Description
```
```
Course courseid (PK) Stores course information such as
course ID, title, description, and
metadata.
Lecture lectureid (PK),
courseid (Partition
Key)
```
```
Central table that stores lecture
records with extracted content,
AI-generated summaries, status flags,
and timestamps. Linked to quizzes.
QuizQuestion questionid (PK),
lectureid (Partition
Key)
```
```
Stores quiz questions with text,
options, correct answer, explanation,
and difficulty. Supports analytics
across lectures.
Quiz quizid (PK),
lectureid
```
```
Stores a quiz entity per lecture.
Contains a list of associated question
IDs. Kept separate from questions to
support modularity and analytics.
UserProgress userid + lectureid
(Composite Key)
```
```
Tracks individual student progress,
including completion status, quiz
scores, and attempt count. Supports
adaptive feedback and progress
tracking.
```
To support performance at scale, the system relies on partition key design, denormalization, and
schema flexibility. Relationships between items are handled using compound keys and naming
conventions rather than traditional joins. All data operations are routed through AppSync, en-
forcing application-level logic. For a deeper discussion of access patterns, consistency strategies,
and schema evolution, see Appendix [??].

### 4.9 System Design Evolution

The platform’s development reflects a transition from an abstract, proposal-stage concept to
a deployed, serverless application tailored to educational workflows. While the initial design
emphasized modular NLP integration, the final system incorporates serverless orchestration,
real-time user feedback, and structured educational logic grounded in pedagogical theory.

Initial Design Intent

The early architecture proposed a RESTful backend using pre-trained NLP models (GPT/BERT)
to perform summarization and quiz generation on text extracted from PDF and PowerPoint files.
Document parsing was expected to use Python-based tools, and the frontend was envisioned as
a lightweight content viewer with limited interactivity or role-based control.


Shift to Serverless Amplify Architecture

Early trials exposed orchestration and scaling challenges. AWS Amplify Gen 2 replaced frag-
mented services with an integrated, infrastructure-as-code framework. Amplify’s native support
for AppSync, Cognito, and Lambda enabled event-driven execution, secure authentication, and
accelerated iteration. The DynamoDB schema evolved from flat records to hierarchical struc-
tures linking courses, lectures, and quizzes, with global secondary indexes (GSI) added to support
performance-critical queries.

Document Processing Re-engineered

Initial parsing methods (e.g., PDFMiner, python-pptx) proved inadequate for Lambda envi-
ronments. They were replaced with node-compatible extractors that supported layout fidelity
and robust postprocessing. A pattern-based table detection module was dropped in favor of a
layout-aware heuristic approach that better handled multi-column content and varied formats.

Emergence of Administrative Workflow

The original design lacked human validation. A review and approval workflow was introduced,
enabling admins to edit content before AI processing. An administrative interface was developed
to manage course creation, lecture approval, and quiz validation, all essential for educational
integrity but absent from the proposal.

AI Integration: Pedagogy-Driven Refinement

Initial plans relied on general-purpose outputs from GPT-style models. The final design inte-
grates Gemini 2.0 Flash with zero-shot, role-based prompting tailored to Bloom’s Taxonomy.
This shift brought structure and pedagogical relevance to both summaries and assessments. Ad-
ditionally, safety filters and moderation thresholds were added to meet ethical and institutional
standards.

UI and UX Reframing

The frontend evolved from a passive display into an instructional tool. Markdown rendering,
syntax highlighting, and mobile responsiveness were prioritized to improve content readability.
The interface was restructured to support a pedagogical flow from Course→Lecture→Sum-
mary→Quiz, and progress tracking was enhanced to reflect learning outcomes and student
behavior.

Summary

The platform’s design evolved beyond technical feasibility to meet the demands of educational
viability. Key architectural choices were re-evaluated based on constraints, testing outcomes,
and user needs. This evolution highlights the importance of iterative development, flexibility,
and domain-specific thinking in building usable, production-grade learning systems.


## 5 Implementation

This section documents the platform’s chronological implementation process. It begins with the
AWS-based serverless infrastructure and transitions to developing the core backend components
that form the educational content pipeline. Every architectural decision was taken with oper-
ational pragmatism in mind, balancing performance, scalability, and simplicity. The preferred
strategy was delivering a system with a high degree of automation, low maintenance overhead,
and robust functionality for administrators and learners.

### 5.1 AWS-Based Implementation

#### 5.1.1 Amplify Gen 2 and CI/CD Integration

The implementation began by bootstrapping the project using AWS Amplify Gen 2, which
offered a modern, CDK-based development model. From a single initialization command, the
backend (Lambdas, S3 storage, Cognito authentication, and DynamoDB models), the frontend
(React + Vite), and the CI/CD pipeline were provisioned in tandem. The decision to adopt
Amplify Gen 2 stemmed from its support for defining cloud infrastructure directly in TypeScript,
allowing rapid iteration without context switching.
The Amplify Console was integrated with GitHub to enable automatic builds and deploy-
ments with every push to the main branch. During development, Amplify’s sandbox environment
simulated actual AWS infrastructure locally, enabling validation of resource behavior and inte-
gration before committing to a production environment. All CloudFormation stacks, deployment
stages, and changes were version-controlled to maintain transparency and rollback capability.

#### 5.1.2 Authentication and Role Management

Early in the development, access control was prioritized. Amazon Cognito established a se-
cure authentication flow restricted to email-based login. The group system in Cognito defined
administrators with full backend access, while regular users held read-only permissions. This
distinction was enforced across both the frontend and backend on the interface, routes, and
components rendered conditionally based on group membership. On the backend, fine-grained
IAM roles ensured that only administrators could perform actions such as uploading, approving,
or deleting files.

#### 5.1.3 Data Modeling with DynamoDB

Core entities, like courses, lectures, quiz questions, quizzes, and user progress, were modeled
within DynamoDB using Amplify’s schema builder. This involved defining data shapes and
planning query and access patterns. Sort keys were implemented to support filtering quizzes by
lecture or tracking progress by user. One-to-many relationships were declared to enable efficient
querying without data duplication.
Special attention was given to reserved keywords. For example, the term ”duration” required
aliasing in update expressions due to its protected status within DynamoDB. Additionally,
DynamoDB streams were configured to emit change events when key status fields were updated.
These triggers initiated AI-driven processing workflows, beginning with summarization after
content approval.

#### 5.1.4 File Storage with S3

The file storage layer was implemented using Amazon S3. Only authenticated administrators
could upload documents. All lecture content was stored under protected paths, with IAM policies
enforcing scoped access. Files were organized by course and lecture identifiers to maintain
structured referencing. The frontend upload interface enforced strict validation rules, allowing
only PDF and PowerPoint files. Duplicate detection logic scanned filenames and metadata before


initiating uploads to avoid redundant records. Upon successful upload, an S3 event notification
triggered the corresponding backend Lambda for content extraction.

#### 5.1.5 Lambda Functions and Event Flow

Lambda functions were implemented in parallel with storage and modeling services. Four back-
end Lambda functions were developed, each fulfilling a distinct role in the processing pipeline
with tightly scoped IAM permissions. S3 uploads triggered the first function. It retrieved the
file, identified its format, and executed pre-processing routines tailored to the content type. The
resulting normalized output was stored in DynamoDB with a ”pending review” status. The
second function responded to DynamoDB stream updates. When a lecture was approved, this
function submitted the extracted content to Gemini 2.0 Flash using a single prompt, leveraging
the model’s high token limit for full-context input. A third function is activated upon receipt of
the summarized output. It extracted relevant metadata and issued a new request to Gemini for
quiz generation. The fourth and final function handled the post-processing of quiz data. It val-
idated structure, standardized answer formats and ensured consistency across all records before
being inserted into DynamoDB. Each Lambda function was defined using Amplify’s TypeScript
CDK and deployed with precise access control. Gemini API credentials were managed via AWS
Secrets Manager and injected securely at runtime.

#### 5.1.6 Frontend Integration

Frontend development proceeded once backend services were stable. The interface was built
using React and Amplify UI and organized into modular components, including views for sum-
maries, quizzes, lectures, and admin dashboards. Markdown rendering was enabled with support
for mathematical notation and structured formatting to accommodate educational content. Ad-
ministrator users accessed document upload, content approval, and quiz editing tools. Student-
facing components guided users from summarized content through assessment sequences, while
progress data was stored in DynamoDB and accessed via GraphQL APIs. Routing logic was
implemented using React Router, and the session state, including authentication and permis-
sions, was passed via context providers. Frontend deployment was integrated into the Amplify
CI/CD pipeline, ensuring consistent updates alongside backend changes.

#### 5.1.7 Pipeline Overview

The overall infrastructure pipeline is orchestrated through AWS-native events and services. Once
an administrator uploads a lecture file through the web interface, it is validated and stored in
Amazon S3. This triggers an event that invokes a Lambda function to extract and normalize
the content, which is then stored in DynamoDB with a ”pending review” status.

```
Amazon S3
```
```
File Upload
```
```
AWS Lambda
```
```
File-processingLambda
```
```
Amazon DynamoDB
```
```
"Pending Review"Status: Admin Approval
```
```
AWS Lambda
```
```
Gemini 2.0 FlashSummarize via
```
```
AWS Lambda
```
```
Generate Quizzesfrom Summary
```
```
Figure 7: Pipeline Workflow
```
Each Lambda function is executed statelessly with dedicated IAM roles and securely managed
secrets. Code was version-controlled and integrated into the CI/CD pipeline for repeatable
testing and deployment. This implementation path ensured that every interaction, from file
upload to assessment generation, was captured within AWS infrastructure without reliance on
containers, servers, or external orchestration. The resulting system is lean, cloud-native, and
extensible, supporting structured educational delivery powered by generative AI and governed
by infrastructure-as-code.


#### 5.1.8 Code Repository Outline

The implementation of the platform was developed from scratch using AWS Amplify Gen 2.
The project structure reflects a modular, serverless architecture built with TypeScript, React,
and Lambda functions. Figure [8] presents the high-level repository structure.

```
Figure 8: Project Source Code Structure
```
Original Development

Substantial portions of the system were custom-developed to ensure alignment with the plat-
form’s pedagogical goals and infrastructure requirements. Key original contributions include:

- Backend infrastructure defined programmatically using AWS CDK within Amplify Gen 2.
- A custom document processing pipeline capable of handling both PDF and PowerPoint
    content.
- Summarization and quiz generation services designed using structured prompt engineering
    techniques and integrated via secure Lambda functions.
- A React frontend tailored for structured learning, featuring custom components for content
    delivery, assessment, and progress tracking.
- DynamoDB schema definitions, storage policies, and IAM configurations developed with-
    out reliance on pre-existing solutions.
- Custom-built test suites targeting core backend logic and frontend behavior.

Third-Party Libraries and Adaptations

While the platform prioritizes original development, third-party libraries were incorporated
where appropriate to streamline auxiliary tasks. All integrations were limited in scope, used
under permissive licenses, and supported through additional custom code.


AWS Amplify Resources

- The initial project structure was bootstrapped using the Amplify CLI and adapted from
    (?).
- Authentication and UI scaffolding were extended from the @aws-amplify/ui-react package,
    manually adding role-based controls.
- Data model definitions follow best practices recommended in Amplify documentation but
    were written independently.

Document Processing

- PDF content was extracted using pdf-parse (v1.1.1), with significant adaptations to im-
    prove layout analysis and sentence reconstruction.
- PowerPoint parsing was implemented using jszip and xml2js, with custom traversal logic
    developed for consistent slide-to-text extraction.
- Table detection routines incorporate heuristics inspired by the PDF.js open-source project
    (Apache 2.0 License), extended with custom formatting and structure recovery.

UI Rendering

- Markdown content is rendered using react-markdown and extended to support custom
    block elements and embedded mathematical notation.
- Mathematical expressions are rendered via KaTeX, while syntax highlighting is applied
    using react-syntax-highlighter.
- Styling was extended from Amplify’s UI primitives, with additional customization for
    accessibility and responsiveness.

AI Integration

- Google’s Gemini 2.0 Flash model powered content summarization and quiz generation
    using the official API client.
- Prompt templates, content filtering, and output validation pipelines were designed and
    implemented entirely in-house.

Attribution Summary

The following references and external tools influenced the implementation:

- Project initialization followed the open-source AWS Amplify template linked above.
- Authentication flows adopt AWS Amplify patterns but feature custom group-based logic
    for administrative access.
- Document extraction uses third-party libraries for parsing but implements a custom pipeline
    for metadata detection, formatting repair, and AI input preparation.
- Frontend development leverages React and Amplify UI libraries, but the component logic
    and layout are custom-built.
- All AI interaction logic, including prompt engineering and safety filtering, was developed
    independently, using only Gemini’s client library for communication.

While the project makes effective use of established frameworks and libraries, the core function-
ality, business logic, and integration layers represent significant original development work.


### 5.2 Backend Content Processing Pipeline

This section details the three backend components that form the educational processing pipeline:
document pre and post-processing, content summarization, and quiz generation. Each was de-
veloped sequentially and iteratively refined. They operate independently but are orchestrated
through DynamoDB streams and Lambda invocations to form a cohesive, event-driven sys-
tem. The goal throughout was to construct a transparent pipeline capable of converting raw,
unstructured content into structured, validated educational experiences with minimal human
intervention.

#### 5.2.1 Pre and Post-Processing

The first component in the pipeline is responsible for receiving uploaded documents and trans-
forming them into clean, structured content suitable for downstream AI processing. The system
handles both PDF and PowerPoint formats. Upon file upload, a Lambda function is triggered by
an S3 event. This function determines the document type, routes it to the appropriate extractor,
and manages the initial conversion to text. For PDFs, a custom-built extraction routine parses
raw content using layout-aware heuristics. The process includes detecting and removing page
headers and footers, de-duplicating page numbers, and merging broken sentences that are split
across lines or columns. This was achieved without relying on heavy document layout models to
preserve Lambda execution time and reduce deployment complexity. In place of those, structural
cues such as font weight, line spacing, and keyword density were used to infer logical breaks.
For PowerPoint files, the system parses slide text and speaker notes. A sequence-preserving ex-
tractor walks through each slide and its associated annotations, maintaining presentation order.
Special handling is introduced for bullets, numbered lists, and slide titles to retain semantic
grouping. Text from graphical placeholders is ignored unless tagged with semantic relevance in
the notes section. Once raw extraction is completed, the text undergoes normalization. This
step includes sentence merging, whitespace compression, punctuation smoothing, and removal of
formatting artifacts. The post-processing routine also applies shallow natural language rules to
detect lists, headings, and table-like structures. Where tables are inferred, spacing is preserved
to maintain column alignment in plain text. This hybrid approach avoids full structural tagging
while providing enough signal to support downstream generation.

```
Table 2: AI Prompt Settings – Summarization
Configuration Parameter Value
```
```
Model Gemini 2.0 Flash
Input Full lecture content (up to 1M tokens)
Role Educational content creator
Structure Objectives, Concepts, Examples, Takeaways
Style Markdown
Safety Block High for Harm, Hate, Misinfo categories
Generation Approach Role-play & Zero-shot prompting
Model Parameters Temperature = 0.2, Top-k = 3, Top-p = 0.8
```
The output of this stage is stored in DynamoDB with a ”pending review” status. An admin
interface provides a dashboard for verifying the extracted content and associated metadata.
Admins can approve, reject, or edit the text, and any transition to the approved state triggers
the next phase of the pipeline.


```
Amazon DynamoDB
```
```
Status:
Extract Raw Text "Pending Review"
Post-processing:
Normalization +
Metadata
```
```
AWS Lambda
```
```
Pre-processing
Lambda
```
```
Amazon S3
```
```
Upload Doc S3 Event
```
```
Figure 9: File Processing Flow
```
#### 5.2.2 Summarization

The second component converts the approved content into a structured educational summary.
When an admin flags a document as approved, a stream event in DynamoDB triggers the
Lambda responsible for summarization. This Lambda prepares the text for AI generation by
constructing a prompt and submitting the full content to Gemini 2.0 Flash. Unlike earlier ap-
proaches that required aggressive chunking to meet input limits, Gemini 2.0 Flash supports
input sizes of up to one million tokens. This capability allowed the system to supply the full
lecture content as a single coherent input, preserving context and improving summary cohesion.
This eliminated the complexity of segmenting and reassembling summaries, reducing prompt
fragmentation and alignment errors. Prompt construction for summarization follows a struc-
tured, zero-shot strategy using role prompting. The AI is instructed to act as an educational
content creator and produce a pedagogically aligned summary using Markdown. This design
incorporates Bloom’s Taxonomy and enforces a predefined section layout. Full details on the
prompt structure, formatting, and design rationale are provided in Section [5.2.4]. Prompt
engineering followed a zero-shot strategy, defined by role assignment, output constraints, and
formatting expectations without providing examples. This method supports rapid scaling and
generalization across topics while maintaining output predictability (??). Markdown format-
ting instructions are embedded directly into the prompt to ensure semantic clarity and frontend
rendering consistency. Gemini 2.0 Flash was configured with parameters tailored to the project
use case. A low-temperature setting (0.2) was used to promote consistency and factual accuracy
over creative variance. Top-k was set to 3, limiting token selection to the three most probable
options at each step. This constrained sampling strategy increases the predictability of outputs.
Top-p was also configured to 0.8, allowing the model to consider the top 80% cumulative prob-
ability mass during sampling. The combined use of Top-k and Top-p balances lexical diversity
with output stability. These values and a low temperature of 0.2 were selected to ensure factual-
ity, reduce hallucinations, and reinforce the consistent generation of instructional content. This
balance ensures that the generated summary adheres to the structure while including enough
lexical flexibility to avoid repetition.
To mitigate risks, Gemini’s built-in moderation settings were activated. These included filters
for harassment, hate speech, and misinformation configured at the highest threshold. These
safety layers ensure alignment with ethical AI standards and institutional content policies.

```
Amazon DynamoDB
```
```
Gemini AI Store in DB
```
```
AWS Lambda
```
```
Summarization
Doc Approved Lambda Post-processing
```
```
Figure 10: Summary Processing Flow
```

#### 5.2.3 Quiz Generation

The final component in the backend pipeline is responsible for converting each summary into
a set of assessment questions. Once a summary is generated, it is passed along with relevant
metadata to the Lambda responsible for quiz generation. The Lambda constructs a role-based,
zero-shot prompt that positions the AI as an educational assessment expert. The prompt defines
difficulty distribution, JSON format requirements, and metadata fields to ensure alignment with
Bloom’s Taxonomy. The full prompt structure and design strategy are described in Section
[5.2.4]. Prompt engineering in this module follows the same philosophy as summarization but
adds structural JSON constraints to guide output parsing. The prompt demands strict format
adherence to facilitate automatic extraction. This includes standardized fields for question text,
options, correct answer, explanation, difficulty, and topic. To minimize ambiguity, safety filters
were again applied using Gemini’s moderation settings. The same temperature setting of 0.2
was maintained to prioritize coherence and accuracy. Top-p was also configured to 0.8 to ensure
the model samples from tokens contributing to the top 80% cumulative probability. In parallel,
top-k was set to 3 to restrict selection to the top three most probable tokens at each decoding
step. This combination limits variance while preserving some controlled diversity. In assessment
generation, where factual grounding and consistency are critical, this configuration significantly
reduces ambiguity and improves the reliability of generated questions.

```
Table 3: AI Prompt Settings – Quiz Generation
Configuration Parameter Value
```
```
Model Gemini 2.0 Flash
Input Full summary content
Role Assessment expert
Output Format 10 MCQs (Multiple Choice Questions)
Difficulty Distribution Easy: 30%, Medium: 50%, Hard: 20%
Format JSON with question, options, answer, explanation, and tags
Safety Block High for Harm, Bias, Inaccuracy
Generation Approach Role-play & Zero-shot prompting
Model Parameters Temperature = 0.2, Top-k = 3, Top-p = 0.8
```
Quiz outputs are parsed and standardized before storage. A validation routine removes label
prefixes from answer choices, converts lettered answers into option text, and verifies consistency
between the answer and its explanation. This step is essential because AI outputs can be
syntactically inconsistent, especially under tight token limits. When validation fails, fallback
logic attempts to reconstruct missing elements or exclude problematic questions from the dataset.
Finalized questions are stored as individual records in DynamoDB and grouped by lecture ID.
A separate table links question sets to quizzes, which are then presented to the student via
an interactive frontend interface. During quiz rendering, each question is displayed alongside
radio-button answer selections, and upon completion, the system provides feedback based on
selected responses and embedded explanations.


```
Amazon DynamoDB
```
```
Question Processing& Validation Store Questions
```
```
AWS Lambda
```
```
Quiz Generator
Event Trigger Lambda Gemini AI Link QuestionsCreate Quiz &
```
```
Figure 11: Quiz Processing Flow
```
This pipeline is optimized for reliability rather than adaptability. Questions are pre-generated
during the content approval flow rather than generated on demand. This choice trades person-
alization for stability, ensuring that questions can be reviewed, moderated, and reused without
incurring real-time API costs or risks of inappropriate output. Overall, the backend system
represents a deliberate balance between automation and oversight. AI components are invoked
through structured prompts and validated through downstream normalization, preserving the
pedagogical and ethical standards expected in educational contexts. The Lambda-based ar-
chitecture ensures that each function remains focused, stateless, and independently testable,
aligning with modern serverless best practices while delivering real educational value.

#### 5.2.4 Prompt Engineering Strategy

Both summarization and quiz generation are orchestrated through structured prompts designed
using a consistent engineering framework. Rather than embedding prompt instructions sepa-
rately in each Lambda, this system follows a modular and scalable strategy emphasizing role
prompting, zero-shot construction, and formalized output constraints. These prompts inter-
face human expectations and large language model behavior, ensuring consistent, pedagogically
aligned outputs across tasks.

Design Rationale

Each prompt assigns a professional role to the AI to align tone, scope, and task-specific expec-
tations. Summarization casts the model as aneducational content creator, while quiz generation
defines the AI as aneducational assessment expert. These roles act as cognitive scaffolds, guiding
the model to adopt appropriate instructional behavior. Both prompts rely on zero-shot prompt-
ing to eliminate the need for in-context examples, enabling generalization across new content
domains and minimizing prompt length.
The prompt explicitly defines output formats to ensure structural predictability: Markdown
for summaries and JSON for quiz questions. Summarization prompts include instructions for
section headers, bullet lists, and inline formatting to enable easy frontend rendering. Quiz
prompts define a strict object schema for each question, including fields for difficulty, topic
tag, correct answer, and explanation. These constraints reduce ambiguity, support automatic
parsing, and improve moderation efficiency.

Cognitive Alignment

Both prompts embed Bloom’s Taxonomy to reinforce cognitive depth. Summarization explicitly
requests the inclusion of content that reflects higher-order thinking skills, such as analysis and
synthesis. Quiz prompts specify a difficulty distribution (30% easy, 50% medium, 20% hard) to
cover the full spectrum of recall, application, and critical reasoning (?). These strategies ensure
that generated content is structurally valid and pedagogically meaningful (?).


Model Configuration

Gemini 2.0 Flash is configured with sampling parameters that balance factual consistency with
lexical diversity. A low temperature of 0.2 is used to prioritize determinism, while top-k is set
to 3 and top-p to 0.8. This combination constrains token selection to high-confidence regions
of the distribution while preserving a small degree of variance. These settings are identical
for summarization and quiz generation and were selected to reinforce reliability over creative
fluency. Safety filters are set to the strictest thresholds to minimize hallucinations and ensure
alignment with institutional policies.

Framework Overview

The prompt design process follows a nine-step framework. This structured approach ensures
reproducibility, supports validation, and can be adapted for future prompt-based extensions.

```
Table 4: Prompt Design Framework Using Role and Zero-Shot Prompting
Component Instruction (Action Step)
```
```
Define Role Assign a relevant expert role to the AI to establish tone,
authority, and contextual understanding.
E.g., ”As an educational content creator”, ”As an assessment
expert”
State the Goal Clearly articulate the end task the AI should accomplish,
ensuring the outcome aligns with your objective.
E.g., “Create a comprehensive lesson summary... ”, “Generate
10 multiple-choice questions... ”
Detail the Task Using
Domain Knowledge
```
```
Break the task into specific sub-requirements based on your
understanding of the subject and expected output.
E.g., “The summary should include learning objectives, key
concepts... ” or “For each question, provide options, correct
answer, explanation... ”
Specify Output
Structure
```
```
Define the format and sections the output must include, using
markdown headers, JSON keys, or tables.
E.g., structured markdown with sections, or JSON arrays with
specific fields.
Set Formatting Rules Instruct how the content should be visually organized or encoded.
E.g., “Use numbered lists”, “Use markdown code blocks for code”.
Include Style and Tone Set expectations for tone, audience level, and clarity.
E.g., “Use student-friendly language”, “Keep explanations
concise and instructional”.
Introduce Variable
Inputs
```
```
Use template variables to control prompt flexibility and reuse.
E.g.,{text},{maxWords},{questionCount}, etc.
Define Quality
Constraints
```
```
Specify limits or expectations for length, complexity, depth, or
alignment with learning frameworks.
E.g., “Keep it under{maxWords}words.”, “Align with Bloom’s
Taxonomy.”
Request Output
Sanitization
```
```
Instruct the AI to remove any unnecessary text, disclaimers, or
summarizing language.
E.g., “Return only the formatted content without additional
commentary.”
```

Workflow Illustration

The full workflow of prompt design and application is illustrated below. It shows how each
prompt type moves from role assignment to structured formatting and is deployed in the backend
processing pipeline.

```
PROMPT DESIGN FRAMEWORK
Using Role and Zero-Shot Prompting
```
```
DEFINE ROLE
Assign a relevant expert role to the establish tone, authority, and contextualAI to
understanding.
```
```
STATE THE GOAL
accomplish, ensuring the outcome aligns withClearly articulate the end task the AI should
your objective.
```
```
DETAIL THE TKNOWLEDGEASK USING DOMAIN
Break the task into specific sub-requirements
based on your understanding of the subject andexpected output.
```
```
SPECIFY OUTPUT STRUCTURE
Define the format and sections the output must
include, using markdown headers, JSON keys,or tables.
Instruct how the content should be visually SET FORMATTING RULES
organized or encoded.
Set expectations for tone, audience level, and INCLUDE STYLE AND TONE
clarity.
```
```
Use template variables to control prompt INTRODUCE VARIABLE INPUTS
flexibility and reuse.
```
```
REFINE QUALITY CONSTRAINTS
Specify limits or expectations for length,
complexity, depth, or alignment with learningframeworks.
```
```
REQUEST OUTPUT SANITIZATION
Instruct the AI to return only the desired output
format, removing extra commentary ordisclaimers.
```
```
Figure 12: Prompt Engineering Workflow Diagram
```
Cross-Domain Adaptability

Although this system is built for educational content, the framework is domain-agnostic. Mod-
ifying the assigned role and task-specific instructions can adapt the same pattern to legal brief
summarization, clinical decision support, technical documentation extraction, or customer ser-
vice automation. The underlying principles, clear role definition, explicit structure, cognitive
alignment, and sanitization, remain applicable across domains. This adaptability ensures that
the prompt strategy can evolve as institutional needs and content types change.


## 6 Testing.

### 6.1 Unit Testing

Unit testing was used to verify the correctness of individual components in isolation, ensur-
ing that each module of the system behaved as intended before progressing to integration-level
evaluation. Given the architectural separation between frontend and backend, testing was di-
vided accordingly across React components and AWS Lambda functions, with distinct strategies
tailored to each domain’s requirements.
The unit testing employed Jest as the primary framework, supplemented by the React Testing
Library to validate user interface behavior. This combination allowed the simulation of user
interactions, form handling, and dynamic rendering in a controlled environment. Administrative
components such as ContentManager, CourseForm, and FileUploader were tested for rendering
accuracy, interaction logic, and input validation. The JsonUploader component, which supports
manual upload workflows, was validated for correct parsing and submission of structured data.
Shared components like Authentication and StudentProgress were also tested, focusing on session
state, role-based logic, and real-time UI updates based on quiz interactions and progress tracking.
For each component, tests were written to confirm correct visual output, simulate common
interaction scenarios, and verify state transitions resulting from user input. The asynchronous
nature of certain frontend operations, such as file uploads and API calls, necessitated using
async testing utilities to ensure component behavior remained stable under delayed or failed
requests. This layer of testing was critical for detecting edge-case failures early and establishing
a stable interface foundation for integration testing.
On the backend, unit tests targeted the logic inside serverless Lambda functions. Mock
implementations of AWS SDK services such as S3, DynamoDB, AppSync, and Cognito were used
to isolate logic from external dependencies. The fileProcessor function was tested for correct
content extraction from uploaded documents, while the dataProcessor was verified for proper
state updates and triggering of downstream Lambdas. The summarization and quizGenerator
functions, which rely on structured prompt design and interaction with the Google Gemini
API, were tested for correct invocation, response handling, and storage of generated summaries
and quizzes. Each function was validated under normal, edge, and failure conditions, ensuring
that input validation, fallback mechanisms, and retry logic behaved as expected. Although full
end-to-end testing was scoped out for this release, the combined unit test coverage across both
frontend and backend provided high confidence in individual module reliability. These unit tests
formed the foundation for subsequent integration testing, reducing the likelihood of propagation
errors and helping isolate bugs to specific layers in the architecture. For a complete breakdown
of test cases and their outcomes, refer to Appendix [??].

### 6.2 Integration Testing

Integration testing was conducted in a live AWS testing environment to validate the end-to-end
functionality of the educational content management system. This phase focused on ensuring
seamless inter-service communication and verifying the system’s behavior across AWS-managed
components and the frontend. The testing process aimed to confirm that data flowed correctly
from content upload to AI-powered processing and user-facing interfaces and that authentication,
authorization, and database synchronization worked as expected.
The environment was provisioned using AWS Amplify Gen 2, leveraging infrastructure-as-
code to replicate production-like configurations. Core AWS services tested included AWS S3
for document storage, AWS Lambda for serverless computing, DynamoDB for structured data
management, AppSync for GraphQL APIs, Cognito for identity management, and CloudWatch
and CloudTrail for observability. Each component played a role in orchestrating the automated
pipeline that transforms uploaded educational material into structured learning content.
Integration testing was performed manually, executing real workflows through the frontend
and verifying backend outcomes using AWS tools. AppSync’s built-in query editor was used to


test GraphQL mutations and queries, validate data transformations, and confirm the accuracy
of resolver logic and authorization flows. This allowed rapid iteration and inspection of inter-
actions between the frontend and the data layer through the GraphQL schema. For example,
document uploads initiated via the React interface triggered S3 events, which in turn activated
Lambda functions for file processing and AI-driven summarization, with outcomes persisted in
DynamoDB and surfaced via AppSync to the UI (?).
CloudWatch logs were monitored to ensure that each Lambda function executed in the correct
sequence and passed output to the next stage. Particular attention was paid to asynchronous
behaviors in the summarization and quiz generation steps, where failures in one function could
silently halt the pipeline. Metrics such as invocation counts, durations, and error rates were
collected to identify bottlenecks and exceptions. These logs also validated that retries and
error-handling mechanisms worked as intended (?).
CloudTrail was used to verify IAM policy enforcement and inter-service calls. It recorded
events such as role assumption, S3 access, and AppSync query authorization, enabling the
confirmation of least-privilege configurations and service boundaries. This helped resolve early
issues where incorrectly scoped IAM roles prevented DynamoDB writes or unauthorized API
access (?)
Authentication integration was tested by verifying user sessions through Cognito. This
included checking JWT propagation to AppSync, session handling across login and logout events,
and role-based access control between admin and student accounts. These tests confirmed that
protected routes and data operations respected user permissions (?)
Three core integration paths were tested in depth. First, the content processing pipeline,
from S3 upload through Lambda-triggered AI processing to AppSync surfacing the result, was
validated for correct event chaining, content status updates, and data persistence. Second,
the authentication flow was tested for secure session management and access control. Third,
data synchronization between AppSync and DynamoDB was evaluated for real-time consistency,
particularly after quiz submission and content approval actions.
Testing was supported by a formal test plan, and all findings were recorded through Cloud-
Watch logs, CloudTrail event histories, and AppSync console outputs. Key issues encountered
during testing included asynchronous coordination failures, IAM misconfigurations, and occa-
sional DynamoDB stream propagation delays. These were resolved through improved retry logic,
tighter IAM policies, and enhanced logging.
Integration testing confirmed that the system operates as a cohesive whole. It validated that
AWS services are correctly orchestrated, that data transitions occur without loss or duplication,
and that the frontend reflects backend state changes in real-time. The robustness of these
integrations underpins the platform’s reliability in delivering educational content at scale.
A detailed breakdown of test cases, objectives, and execution steps is provided in Ap-
pendix [??].


## 7 Results

This chapter presents the core outputs of the system developed for automated educational
content processing, structured around three primary components: document processing, content
summarization, and quiz generation. The results are reported objectively, highlighting functional
achievements, performance metrics, and alignment with the project’s intended learning support
objectives. Each subsection corresponds to a distinct stage in the system pipeline, providing
tabulated data, model comparisons, and Bloom’s Taxonomy mappings where applicable. This
chapter is critical to the overall report, providing the factual basis for the following evaluation.
It demonstrates how the system performs in practice and to what extent it delivers on the aims
defined earlier in the project.

### 7.1 Document Processing Pipeline Results

The document processing pipeline demonstrated consistent and reliable performance across all
test cases. A total of 50 documents, comprising university lecture PDFs and PowerPoint slides,
were submitted for processing. All documents were successfully extracted and cleaned, yielding
a 100% success rate. The unified Lambda processing function showed minimal variance in
runtime, with an average of 1.564 seconds per document. No failures were recorded, indicating
robustness in the file parsing and cleanup pipeline. The sample output snippet from one lecture
file (“COMP1811-lecture1-Variables”) confirms successful normalization and text extraction,
preserving bullet-point structure.

```
Table 5: Document Processing Component Results
```
```
Metric Description Result
```
```
Total Uploads
Attempted
```
```
Total number of documents
uploaded to the system
```
##### 50

```
Successful Processing
Count
```
```
Documents successfully
extracted and cleaned
```
##### 50

```
Processing Failures Uploads that failed due to
format, parsing, or runtime
errors
```
##### 0

```
Success Rate (%) Percentage of successful
processing out of total
uploads
```
##### 100%

```
Average Processing
Time (s)
```
```
Mean runtime of the unified
processing Lambda function
```
```
1.564 s
```
```
Max Processing Time
(s)
```
```
Longest recorded document
processing duration
```
```
1.955 s
```
```
Min Processing Time
(s)
```
```
Shortest recorded document
processing duration
```
```
0.953 s
```
```
Sample Document ID Identifier of a successfully
processed file
```
```
COMP1811lecture1Variables
```
```
Sample Cleaned
Output (Truncated)
```
```
Example output returned by
the Lambda function after
extraction and cleanup
```
- General concept/pattern
for most computer
programs/applications: ...


```
Figure 13: Content Extraction Comparison Sample
```
### 7.2 Summarization Results

All 50 processed documents were passed to the summarization module, resulting in a 100%
generation success rate. Using the Gemini API, each summary included structured content with
learning objectives, key concepts, applications, and takeaways, aligning with Bloom’s taxonomy.
On average, input documents contained ̃3,000 tokens, with output summaries distilled to ̃2,200
tokens, ensuring content conciseness without significant loss of information.

```
Table 6: Content Summarization Component Results
Metric Description Result
```
```
Total Summarization
Requests
```
```
Documents submitted for
AI-based summarization
```
##### 50

```
Successful
Summarizations
```
```
Valid structured summaries
returned by the system
```
##### 50

```
Summarization Success
Rate (%)
```
```
Percentage of successful
generations
```
##### 100%

```
Average Summarization
Time (s)
```
```
Mean time for summary
generation using Gemini API
```
```
15.209 s
```
```
Max Summarization
Time (s)
```
```
Longest summarization
duration observed
```
```
22.533 s
```
```
Min Summarization
Time (s)
```
```
Shortest summarization
duration observed
```
```
11.833 s
```
```
Token / Word Statistics Structured summaries align
with desired length
constraints and content
density
```
```
Average input: 3,000 tokens
( 2,250 words); Output: 2,200
tokens ( 1,650 words)
```
```
Sample Summary
Output (Truncated)
```
```
Example learning objectives
generated by the system
```
- Understand the purpose
and definition of
variables in programming.
...


Bloom’s Taxonomy Mapping

Summarized content was mapped to Bloom’s levels, showing a balanced distribution across
“Understand” and “Apply” categories (42.9% each), with foundational elements classified as
“Remember” (14.3%).

##### 42.9% 14.3%

##### 42.9%

```
Remember
Understand
Apply
```
```
Figure 14: Bloom’s Taxonomy Distribution in Corrected Summary Content
```
```
Table 7: Bloom’s Taxonomy Mapping of Summary Content (One Sample)
```
```
Section Bloom’s Level Justification
```
```
Variables: Purpose,
Definition, and Memory
Allocation
```
```
Understand Introduces core concepts explaining
how variables work and how memory
is allocated.
Variable Assignment and
Reassignment
```
```
Apply Demonstrates assignment,
reassignment, and swapping with code
examples, requiring practical use.
General
Input-Process-Output
Pattern
```
```
Understand Describes a conceptual model; helps
learners understand the structure of
typical programs.
Simple I/O Mechanisms:
input()andprint()
```
```
Apply Involves direct use of I/O functions,
syntax understanding, and behavior in
runtime.
Constants Remember Defines the convention for constant
declaration without requiring
application or reasoning.
Variable Naming Rules and
Conventions
```
```
Understand Explains how to interpret naming
standards and avoid common
mistakes.
Common Variable Naming
Conventions (PEP8)
```
```
Apply Requires learners to apply PEP8 style
rules to their code in practical
scenarios.
```

ROUGE Score Benchmarks

ROUGE evaluations were performed across five summarization models. Gemini-2.5-pro ranked
highest in the qualitative assessment but showed a lower ROUGE-1 F1-score (0.3508) compared
to Gemini-2.0-flash (0.3950). DeepSeek performed poorly across all metrics, confirming prior
qualitative assessments.

```
Gemini-2.5-pro Gemini-1.5-flash Gemini-2.0-flash
Gemini-1.5-flash-8b DeepSeek-R1-1.5b
```
```
0.0
```
```
0.1
```
```
0.2
```
```
0.3
```
```
0.4
```
```
0.5
```
```
Score 0.268
```
```
0.318
```
```
0.368 0.382
0.320
```
```
0.508
```
```
0.283
```
```
0.426
```
```
0.234
```
```
0.127
```
```
0.351
0.300
```
```
0.395
```
```
0.290
```
```
0.181
```
```
ROUGE-1 Scores by Model
Precision
Recall
F1-score
```
```
Figure 15: Summarization ROUGE-1 scores (Precision, Recall, F1) for each model
```
```
Gemini-2.5-pro Gemini-1.5-flash Gemini-2.0-flash
Gemini-1.5-flash-8b DeepSeek-R1-1.5b
```
```
0.000
```
```
0.025
```
```
0.050
```
```
0.075
```
```
0.100
```
```
0.125
```
```
0.150
```
```
0.175
```
```
0.200
```
```
Score
0.076
```
```
0.096
```
```
0.143
0.121
```
```
0.062
```
```
0.158
```
```
0.078
```
```
0.181
```
```
0.069
```
```
0.020
```
```
0.102
0.086
```
```
0.160
```
```
0.088
```
```
0.030
```
```
ROUGE-2 Scores by Model
Precision
Recall
F1-score
```
```
Figure 16: Summarization ROUGE-2 scores (Precision, Recall, F1) for each model
```

```
Gemini-2.5-pro Gemini-1.5-flash Gemini-2.0-flash
Gemini-1.5-flash-8b DeepSeek-R1-1.5b
```
```
0.00
```
```
0.01
```
```
0.02
```
```
0.03
```
```
0.04
```
```
0.05
```
```
0.06
```
```
0.07
```
```
0.08
```
```
Score
```
```
0.023
```
```
0.034
```
```
0.060
```
```
0.040
```
```
0.012
```
```
0.049
```
```
0.027
```
```
0.078
```
```
0.023
```
```
0.004
```
```
0.031 0.030
```
```
0.068
```
```
0.029
```
```
0.006
```
```
ROUGE-3 Scores by Model
Precision
Recall
F1-score
```
```
Figure 17: Summarization ROUGE-3 scores (Precision, Recall, F1) for each model
```
```
Gemini-2.5-pro Gemini-1.5-flash Gemini-2.0-flash
Gemini-1.5-flash-8b DeepSeek-R1-1.5b
```
```
0.0
```
```
0.1
```
```
0.2
```
```
0.3
```
```
0.4
```
```
0.5
```
```
Score 0.254
```
```
0.294
```
```
0.344 0.361
0.306
```
```
0.481
```
```
0.262
```
```
0.398
```
```
0.221
```
```
0.121
```
```
0.332
0.277
```
```
0.369
```
```
0.274
```
```
0.174
```
```
ROUGE-L Scores by Model
Precision
Recall
F1-score
```
```
Figure 18: Summarization ROUGE-L scores (Precision, Recall, F1) for each model
```
LLM as a Judge Score

This figure presents the overall G-EVAL scores generated by GPT-4.1 using chain-of-thought
prompting to complement the ROUGE-based lexical evaluations. Each model has assessed
over 50 summarisation outputs based on factual consistency, relevance, informativeness, and
conciseness. These scores provide a human-aligned measure of quality and reveal meaningful
differences in clarity and alignment not captured by ROUGE alone.


```
Gemini-2.5-pro Gemini-1.5-flash Gemini-2.0-flash
Gemini-1.5-flash-8b DeepSeek-R1-1.5b
```
```
0
```
```
1
```
```
2
```
```
3
```
```
4
```
```
5
```
```
Overall Score
```
```
4.83 4.77 4.72 4.61
```
```
3.01
```
```
Overall Scores of Summarization Models
```
```
Figure 19: Overall scores of summarization models over 50 summarizations
```
### 7.3 Quiz Generation Results

The quiz generation module was executed over 10 AI-generated summaries, producing a total
of 100 multiple-choice questions. Each quiz contained 10 questions, covering key aspects of the
lecture, with consistent formatting and a distribution of Bloom’s taxonomy.

```
Table 8: Quiz Generation Component Results
Metric Description Result
```
```
Total Quizzes Generated Summaries passed to AI for
quiz creation
```
##### 10

```
Successful Quiz
Generations
```
```
Quiz sets successfully
generated and stored
```
##### 10

```
Quiz Generation Success
Rate (%)
```
```
Percentage of successful quiz
generations
```
##### 100%

```
Average Quiz Gen Time
(s)
```
```
Mean time to generate a full
quiz per lecture
```
```
8.719 s
```
```
Total Questions
Generated
```
```
Cumulative number of quiz
questions generated
```
##### 100

```
Average Questions per
Lecture
```
```
Mean number of questions
per quiz set
```
##### 10

```
Token / Word Statistics Quiz outputs are concise and
optimized for knowledge
recall assessments
```
```
Average input: 3,000 tokens
( 2,250 words); Output: 1,200
tokens ( 900 words)
Sample Quiz Output
(Truncated)
```
```
Example question and
answer pair
```
```
Q: What is the primary
purpose of a variable in
programming? A: To store
and manipulate data.
```

The quiz questions displayed varied complexity. One example set demonstrated a majority
of “Understand” level questions (50%), followed by “Remember” (20%), “Apply” (20%), and
“Analyze” (10%).

##### 20%

##### 20%

##### 10%

##### 50%

```
Remember
Apply
Analyze
Understand
```
```
Figure 20: Distribution of Quiz Questions by Bloom’s Taxonomy Level (One Quiz Sample)
```
```
Table 9: Bloom’s Taxonomy Justification of Quiz Questions (One Quiz Sample)
```
```
Q# Question (Paraphrased) Bloom’s Level Justification
```
```
1 What is the output ofx + ywhenx
= 5andy = "10"?
```
```
Analyze Requires understanding type
error and evaluating code
behavior.
2 What data type is returned by
input()?
```
```
Remember Direct recall of a Python
function’s return type.
3 What is the purpose of a variable? Understand Involves grasping the role of
variables in code.
4 Which variable name best follows
PEP8?
```
```
Apply Requires applying a known
coding convention.
5 What is the naming convention for
constants, and why?
```
```
Understand Understanding both the rule
and rationale.
6 What happens when you reassign a
variable with a different type?
```
```
Understand Requires understanding
Python’s dynamic typing.
7 Which operator assigns a value in
Python?
```
```
Remember Recall of specific syntax.
```
```
8 What is the purpose of theprint()
function?
```
```
Understand Tests comprehension of basic
output functionality.
9 Which is a valid variable name? Apply Requires applying naming
rules correctly.
10 What best describes the
Input-Process-Output pattern?
```
```
Understand Requires grasping the overall
structure of a program.
```

```
Gemini-2.5-pro Gemini-1.5-flash Gemini-2.0-flash
Gemini-1.5-flash-8b DeepSeek-R1-1.5b
```
```
0
```
```
1
```
```
2
```
```
3
```
```
4
```
```
5
```
```
Score (out of 5)
```
```
4.82 4.65 4.71 4.63
4.18
```
```
4.75 4.52 4.58 4.48
4.12
```
```
4.78 4.58 4.64 4.55
4.15
```
```
4.65 4.35 4.42
4.31
3.85
```
```
LLM-as-a-Judge QA Evaluation Scores
```
```
Q Relevance Answerability QA Overall Answer Correctness
```
```
Figure 21: Average QA evaluation scores (relevance, answerability, correctness) per model
```
### 7.4 Conclusion

This section presented the core results of the developed AI-powered education support tool.
Document processing exhibited robust, error-free execution. Summarization achieved concise,
structured outputs aligned with learning objectives and high ROUGE scores from top-performing
models. Quiz generation demonstrated consistent structure and alignment with Bloom’s tax-
onomy, with no failures during generation. The findings presented here form the foundation
for the next section, which critically evaluates performance, highlights limitations, and explores
implications for future deployment and academic integration.


## 8 Evaluation

### 8.1 Evaluation Criteria and Methodological Reflection

The evaluation of this project focused on two primary outputs: the accuracy and faithfulness of
AI-generated summaries and the alignment and cognitive quality of quiz questions. Given the
absence of a golden dataset or validated ground truth for either summarisation or assessment,
the evaluation framework combined traditional and novel approaches. ROUGE was used to
benchmark summarisation fidelity, and G-EVAL, a recent GPT-4.1-powered evaluation protocol,
was employed as an LLM-based judge for summarisation and quiz components.
The choice of G-EVAL was deliberate. In the context of abstractive summarization and
multiple-choice question generation, where lexical overlap (as measured by ROUGE) fails to
capture nuances like factuality or alignment, LLM-based evaluators offer critical advantages.
G-EVAL uses chain-of-thought prompting to enforce structured evaluation criteria and provides
probabilistic scoring aligned with human judgment, addressing key limitations in traditional
methods (?). This was especially important for quiz evaluation, where comparing questions
against the source text using n-gram metrics would be inappropriate and misleading. The
evaluation relied on five key criteria: factual consistency, relevance, informativeness, conciseness,
and an overall composite score.
On the development side, the methodology adopted for the project, Action Research inte-
grated with Agile SCRUM, proved effective for building and iterating a research-driven artifact.
The Agile framework supported modular integration of summarisation, quiz generation, and
evaluation layers. The Action Research cycles allowed iterative reflection and refinement. This
approach ensured tight alignment between theory and implementation: pedagogical principles
informed prompt structure, technical outcomes fed into weekly reviews, and empirical outputs
guided ongoing design decisions (??).
From a tooling perspective, the system leveraged AWS services such as Amplify, Lambda, Dy-
namoDB, and S3 to construct a serverless and scalable pipeline. This cloud-native architecture
allowed easy parallelization of summarization tasks, model benchmarking, and asynchronous
quiz evaluation without introducing complexity to the deployment pipeline. The result was a
modular backend architecture capable of future extension and robust experimentation (?).

### 8.2 Evaluation Against Project Objectives

This section evaluates the extent to which the project met its original objectives. The analysis
draws directly on the system outputs presented in section [7] and is supported by relevant
evaluation metrics and literature.

Objective 1: Develop a summarisation pipeline

The summarization component consistently produced high-quality structured outputs from all
50 processed documents (see Table [6]). The pipeline, which integrates document extraction,
Gemini model summarisation, and Bloom’s-aligned structuring, met the functional requirements.
ROUGE scores confirmed lexical fidelity (e.g., Gemini-2.0-flash achieved the highest ROUGE-1
F1 score of 0.3950), while G-EVAL scores (Figure [19]) validated relevance, factual consistency,
and conciseness.
In addition to performance metrics, the outputs were pedagogically aligned. Summaries
captured learning objectives and key explanations with minimal hallucination, confirmed by
taxonomy mapping and model comparisons. This objective is considered fully achieved.

Objective 2: Build an assessment generation module

The quiz generation module successfully produced 10-question sets for each of the 10 evaluated
lectures, yielding a 100% generation success rate (Table [8]). Bloom’s taxonomy coverage was


consistent, with most questions falling under ”Understand” and ”Apply” levels, and examples
of higher-order ”Analyze” level questions observed (see Figure [20] and Table [9]). G-EVAL was
used to evaluate quiz quality regarding answerability, relevance, and correctness (Figure [21]).
The results showed a high degree of alignment between generated questions and the summarised
content, validating the effectiveness of the structured prompt design and post-processing filters.
However, the approach is constrained by the number of relevant, content-aligned questions
that can be generated per lecture. The current ceiling is 10–20 questions per lecture without
introducing hallucinations or drifting from the source material. Scaling to topic-level question
generation could increase diversity, though this introduces risks when topic depth is shallow.
This objective is considered mostly achieved, with defined limitations.

Objective 3: Design a cloud-native application

The project was successfully deployed as a serverless, cloud-native solution using AWS Amplify,
Lambda, S3, DynamoDB, and AppSync. The architecture allowed modular deployments and
secure role-based access, as described in Section [4.1]. All components were tested end-to-
end, including authentication, storage, summarisation, and quiz generation. The architecture
enabled asynchronous evaluation with minimal latency, evidenced by average summarisation
times (15.209s) and quiz generation times (8.719s). This objective is considered fully achieved.

Objective 4: Integrate structured prompt design

Prompt engineering was applied across both summarisation and quiz components. Techniques
such as role prompting, zero-shot prompting, and content scaffolding were used to guide model
outputs. Outputs were aligned with Bloom’s Taxonomy (Tables [7] and [9]), and hallucination
was minimized through prompt refinement, educator-in-the-loop filters, and strict model config-
uration. Specifically, the generation model was set with temperature 0.2, top-p 0.8, and top-k
3 to ensure controlled and predictable outputs. Safety settings were also enforced to eliminate
the risk of harmful or inappropriate language. The prompt design was informed by established
frameworks and validated through both ROUGE scores and G-EVAL ratings. The effectiveness
of structured prompting was evident in the consistent tone, structure, and pedagogical alignment
of the outputs across different model variants. This objective is considered fully achieved.

Objective 5: Validate outputs using automated metrics

The project implemented a dual evaluation framework using ROUGE (for summaries) and G-
EVAL (for both summaries and quizzes). This approach allowed benchmarking without human-
annotated datasets and was supported by recent literature (??). The combination of traditional
and LLM-aligned evaluators enabled both quantitative and qualitative comparisons of model
outputs. For example, Gemini-2.5-pro had the best factual and structural alignment per G-
EVAL, even though it ranked lower in ROUGE scores. This highlights the limitations of single-
metric evaluation and reinforces the value of the chosen hybrid approach. This objective is
considered fully achieved.

### 8.3 Comparative and Critical Analysis

The system’s performance was benchmarked against multiple summarisation models to establish
its standing in the absence of a gold-standard dataset. As reported in Section [7], Gemini-2.5-pro
was ranked highest by G-EVAL based on clarity, factual alignment, and instructional structure,
despite achieving only mid-range ROUGE-1 F1 scores (0.3508). In contrast, Gemini-2.0-flash
scored highest in ROUGE (0.3950) but was noted for repetitiveness and reduced refinement. This
divergence illustrates a key limitation of reference-based metrics: lexical overlap alone fails to
capture semantic or pedagogical quality, a concern also raised in recent comparative evaluations


of LLMs (??). These rankings were based on qualitative G-EVAL outputs, summarised in Table
[10].

```
Table 10: Summary Evaluation Rankings by Model
Rank Model Justification
```
```
1 Gemini-2.5-pro Most accurate and balanced. Detailed, clear, and faithful to the
source without digression or excess.
2 Gemini-1.5-flash Highly structured and deep, with accurate technical conventions
and strong example use.
3 Gemini-2.0-flash Comprehensive but more repetitive and less refined than
top-ranked summaries.
4 Gemini-1.5-flash-8b Accurate, but introduces minor deviation (e.g., emphasis on
“data types”) and has noted repetition.
5 DeepSeek-R1-1.5b Factually inconsistent, omits key topics, introduces incorrect
conventions—fails to meet academic integrity standards.
```
G-EVAL filled this gap by enabling fine-grained evaluation of summarisation and quiz outputs
without requiring human-written references. Its chain-of-thought prompting and probabilistic
scoring methodology allowed the system to assess outputs across multiple dimensions, includ-
ing relevance and factual consistency. This was especially valuable for quiz evaluation, where
reference metrics like ROUGE would not have meaningfully reflected alignment with content
or cognitive depth. The use of G-EVAL also highlighted key differences in model behavior.
DeepSeek-R1-1.5b, for example, produced summaries with notable factual inconsistencies and
deviations from the source, as reflected in both G-EVAL scores and qualitative analysis. Its
ROUGE scores were the lowest among all tested models, confirming alignment between tra-
ditional and LLM-based metrics in clear failure cases (see Figures [7.2]). This triangulation
further validated the robustness of the evaluation setup. In terms of content generation, the
system succeeded in maintaining alignment between summarised material and assessment items,
but current quiz generation is limited to 10–20 relevant questions per lecture. As discussed in
Section [7.3], attempts to scale beyond this often resulted in hallucinated or tangential questions.
While this constraint ensures high alignment and avoids content drift, it does limit the system’s
flexibility for large-scale assessment pools. One possible future approach is topic-based question
generation, which could increase coverage per lecture, though topic granularity would need to
be validated to avoid shallow or repetitive outputs. Compared to existing systems, this project
delivers both summarisation and quiz generation, whereas PDFQuiz focuses solely on quiz cre-
ation and does not disclose its internal implementation.? presents a GPT-2-based pipeline
for generating Overviews and Reflection quizzes within adaptive narrative learning pathways.
Their focus is on dynamic content integration and pathway-level adaptation, while this project
processes static academic materials and emphasizes fidelity, structured prompting, and evalua-
tion. Kinnu reinforces learning through spaced repetition and flashcards rather than structured
quizzing. While there is conceptual overlap in supporting knowledge recall, this project is dis-
tinct in combining summarised instructional content with generated assessments and validating
both using LLM-as-a-judge metrics to ensure output alignment and correctness. Finally, the
project’s integration of serverless architecture with model benchmarking and pedagogical vali-
dation positions it as a novel contribution to educational AI tooling. The ability to deploy and
evaluate AI-generated content across summarisation and assessment tasks, with alignment to
learning outcomes, represents a meaningful advancement over existing academic and commercial
alternatives.


### 8.4 Challenges and Limitations

Several limitations were identified during implementation and evaluation, both technical and
methodological. While the system performed reliably across summarisation and quiz generation
tasks, certain constraints limited scalability and generalization. One key limitation lies in the
scalability of quiz generation. The current technique can reliably generate 10 to 20 questions
per lecture while maintaining alignment with the source material. Generating more than that
could result in questions that, although topically relevant, are not grounded in the original
content. This presents a risk of hallucination and undermines the reliability of the assessments.
A potential solution would be to shift toward topic-level question generation, where each topic
within a lecture (typically 5–6 per lecture) produces a distinct set of questions. However, this
introduces challenges: not all topics are equally developed, and in cases where coverage is brief,
maintaining alignment becomes difficult again. In the absence of human-annotated datasets or
existing baselines, evaluating output quality required proxy techniques. ROUGE was used for
lexical overlap in summaries but lacks sensitivity to meaning and pedagogical intent. G-EVAL,
while effective as a human-aligned evaluator, is not without risk. As an LLM judges other
LLM outputs, its decisions may carry bias or overestimate fluency and alignment. However,
the structured prompting used in G-EVAL and its high agreement with human annotators in
recent literature mitigate this concern to an extent (??). Data limitations also played a role.
The limited dataset size (50 documents for summarisation, 10 for quiz generation) restricts the
generalisability of the evaluation. Although the results are internally consistent and backed by
strong alignment scores, broader deployment would require testing on a wider range of subject
areas and document types. Finally, real user validation was not conducted due to time and
ethical constraints. All results were evaluated offline using automated techniques and simulated
student runs. While effective for benchmarking, this limits the ability to assess the platform’s
usability, instructional effectiveness, and student engagement in a real-world context.

### 8.5 Conclusion

This chapter evaluated the system’s ability to meet the project’s aim: supporting student knowl-
edge recall through automated summarisation and structured assessment generation. The results
demonstrated that the system processed academic documents reliably, generated pedagogically
aligned summaries, and produced quizzes that reflected appropriate cognitive levels. These
outputs were evaluated using both traditional metrics (ROUGE) and a structured LLM-based
protocol (G-EVAL), confirming performance across fidelity, relevance, and content alignment.
Most objectives were fully achieved. The summarisation pipeline and assessment module oper-
ated with consistent accuracy and content quality. The prompt design and evaluation framework
ensured that outputs remained pedagogically valid and free from major hallucinations. The only
notable limitation was the controlled scale of quiz generation, which, while intentional, constrains
flexibility. The potential for topic-level question expansion exists but would require safeguards
to maintain alignment. While user-facing validation and large-scale testing remain out of scope,
the results align with initial expectations. Integrating Action Research and Agile allowed rapid
iteration and informed decision-making throughout development. The use of serverless cloud
infrastructure further confirmed the feasibility of deploying and scaling such a system in real-
world educational settings. The findings presented here establish a foundation for continued
development and integration. The next chapter outlines the overall conclusions of the project,
including its broader contributions and areas for future work.


## 9 Legal, Social, Ethical and Professional Issues

The development of an AI-driven educational support system carries a range of interconnected
legal, social, ethical, and professional considerations. While the system was developed within
an academic research context and not deployed commercially, all relevant issues were reviewed
with a forward-looking perspective, particularly regarding the system’s potential real-world use
and implications.
From a legal standpoint, the system was designed to avoid processing personal data, thereby
ensuring compliance with the UK Data Protection Act 2018 and GDPR principles. All docu-
ments processed were academic materials, devoid of identifiable user data. If this system were
scaled for commercial use in an educational platform, further controls would be required to
ensure that user interaction data is anonymized, securely stored, and handled with appropriate
consent mechanisms. Data privacy policies and user rights to access, modify, or delete their data
would need to be implemented, with a potential requirement for registration with the Informa-
tion Commissioner’s Office (ICO) if sensitive data were collected (??). Regarding copyright,
care was taken to ensure that no university content was redistributed. Summaries and quiz
questions are generated dynamically and stored internally for educational purposes only. How-
ever, copyright licensing for source materials would be essential if this system were deployed in
a real academic institution or commercial setting. Additionally, API usage from services like
Gemini and AWS was conducted in line with their terms of service, and any commercialization
would require ensuring continued compliance with licensing restrictions and usage limits.
Ethically, the system addresses a number of potential concerns. Large language models are
known to hallucinate, introduce bias, or produce misleading content. These risks were man-
aged through prompt engineering, educator-in-the-loop validation, and safe decoding strategies
(temperature 0.2, top-p 0.8, top-k 3), as well as the use of content safety filters provided by
the Gemini API. Further, content quality was evaluated using G-EVAL. This LLM-as-a-judge
technique aligns with recent research advocating for structured, scalable content evaluation in
the absence of gold-standard human references (?). While this does not remove all risk, the
system does provide transparency and traceability, as every summary and quiz question can be
mapped back to its original source.
Socially, the tool was developed to support, not replace, educators. It enhances content re-
call and comprehension through structured summaries and assessments, but the interpretation
and teaching of complex topics remain the responsibility of human instructors. The system
was designed with accessibility in mind, providing concise, readable output. No offensive or
inappropriate language was produced during testing, and safety configurations were actively
maintained to enforce this. However, in a real-world deployment scenario, further accessibil-
ity features would need to be added, such as screen reader support, high-contrast mode, or
alternative content formats to accommodate diverse learners (???).
Professional responsibilities were considered throughout development, aligning with the BCS
Code of Conduct. The project maintained integrity by disclosing model use and evaluation lim-
itations, using only openly accessible data, and adopting a design approach that foregrounded
educational value and user safety. The system’s modularity and traceability allow future devel-
opers or educators to review, revise, or remove content as necessary, promoting accountability
(??). If deployed commercially, several additional challenges would arise. These include de-
termining data and model ownership, managing API costs at scale, ensuring uptime across
serverless components, and supporting long-term content maintenance. There would also be a
need for formal content audits, documentation, and user onboarding processes to ensure ethical
use and trust in the system (?).
In summary, this project has addressed key legal, ethical, and professional issues relevant
to AI in education. While the current implementation is research-focused and internally vali-
dated, it is built with the expectation that future deployment would require robust compliance
mechanisms, educator oversight, and a continuous commitment to responsible AI development.


## 10 Conclusion and Future Work

This chapter brings together the project’s key contributions, outcomes, and reflections while
identifying opportunities for future work. The system aimed to create an AI-supported edu-
cational tool that transforms academic materials into structured summaries and assessments,
enabling learners to engage in more effective self-directed study. Throughout this project, the
artifact and the development process have yielded insights that inform its position within the
broader educational and AI ecosystems.

### 10.1 Summary of the Work

The project’s core objective was to build a supportive AI system that automates two essential
educational tasks: summarising university-level course materials and generating knowledge re-
call assessments. The implemented system allows admin users to upload academic documents,
which are then summarised using a large language model (LLM). Students interact with the
system by accessing these summaries and taking associated quizzes. Each of these stages was
completed as planned. The project was delivered using a serverless, cloud-native architecture
built entirely on AWS services, including Amplify, Lambda, DynamoDB, AppSync, and Cognito.
This backend handled document ingestion, processing, model invocation, and storage securely
and scalable. The summarization component used the Gemini API to generate structured, ped-
agogically aligned summaries. Quiz generation prompts were designed to maximize clarity and
relevance and then passed to the same LLM for question construction. Content quality was
evaluated using a two-pronged strategy: ROUGE scores for lexical similarity and G-EVAL us-
ing GPT-4.1 for human-aligned, structured judgment. Significant effort was placed on aligning
generated content with learning objectives, reducing hallucinations, and ensuring factual con-
sistency. Each component was iteratively improved through Action Research cycles, supported
by Agile SCRUM project management and weekly milestone reviews.

### 10.2 Summary of Outcomes

The project successfully met its aim and all five objectives. The summarization pipeline achieved
a 100% success rate across 50 documents, with clear, structured outputs aligned to Bloom’s
Taxonomy levels. ROUGE scores validated lexical consistency, while G-EVAL ratings confirmed
factuality, clarity, and relevance. Among tested models, Gemini-2.5-pro ranked highest for
balance and quality despite not producing the best ROUGE score, highlighting the importance of
human-aligned evaluation. Quiz generation was also successful, with ten quizzes generated (one
per summary), each containing ten questions with cognitive diversity. Bloom’s-level mapping
confirmed that most questions fell under ”Understand” and ”Apply,” with several examples at
the ”Analyze” level. G-EVAL was again used to validate question answerability and correctness,
offering a meaningful alternative to ROUGE, which is less suitable for evaluating questions.
The system infrastructure handled all tasks within acceptable latency thresholds, with average
processing times of 15.2 seconds for summarisation and 8.7 seconds for quiz generation. No
processing failures occurred throughout testing.

### 10.3 Significance and Positioning

This project contributes a fully modular, cloud-native educational AI system with a transpar-
ent evaluation pipeline and pedagogical alignment, characteristics often absent in existing tools.
Unlike closed systems such as PDFQuiz, this project provides open explanations of its process
and design. While some commercial platforms like Kinnu support learning, they focus on spaced
repetition and flashcard-style interactions. In contrast, this system generates full-length sum-
maries and quizzes designed to support knowledge recall and formative assessment. Compared
to Diwan et al. (2023), who focus on narrative-based learning pathway augmentation, this
project is centered around static academic documents and content fidelity. The emphasis here is
not on adapting the narrative journey but ensuring that the AI-generated content stays faithful


to the original material. This is particularly relevant in formal academic settings where fidelity
and correctness are paramount. The integration of G-EVAL as an evaluation framework is also
novel. It enables structured, scalable assessment of AI-generated content when no gold-standard
answers or datasets exist. The project thus contributes a technical artifact and a methodological
approach to evaluating educational LLM outputs without requiring human baselines.

### 10.4 Limitations and Reflections

While most objectives were fully achieved, the project also encountered limitations. Quiz gener-
ation was intentionally capped at 10–20 questions per lecture to avoid hallucinations. Attempts
to scale beyond this often resulted in questions that were either too vague or not aligned with
the original text. A proposed solution, generating questions per topic, could extend the pool
to 50 or more, but this introduces variability in topic depth, potentially reintroducing the same
alignment issues at a finer granularity. The use of G-EVAL was effective, but it is not with-
out its drawbacks. As a large language model evaluating other LLM outputs, it can exhibit
bias, particularly favoring fluent or well-structured but subtly incorrect answers. However, in
this project, the structured prompting design and evaluation dimensions were chosen carefully
to help mitigate this risk in practice. Time and resource constraints prevented broader user
testing. While educator-in-the-loop validation was used for iterative prompt development, no
structured user trials were conducted. This limits insight into real-world usability, engagement,
or the system’s effect on learner outcomes. A notable challenge throughout the project was the
learning curve associated with AWS technologies. As the project was developed from scratch
using AWS Amplify and related services, extensive time was invested in understanding the
documentation, navigating the platform’s architectural paradigms, and debugging deployment
issues. While technically demanding, this experience proved to be a valuable aspect of the
project. It significantly deepened the understanding of scalable, event-driven infrastructure and
clarified the real-world trade-offs between control, cost, and deployment velocity in cloud-native
development.

### 10.5 Future Work

While the system has achieved its core objectives, there are clear opportunities to expand and
refine its functionality:

Enhanced Assessment Diversity

Current assessments are limited to multiple-choice questions. Future iterations could explore
different types of assessments.

Retrieval-Augmented Generation (RAG)

Future versions could use vector databases or semantic search to feed relevant passages to the
LLM during generation to reduce hallucination and enhance factual grounding. This would
anchor content more directly in the source material and support more accurate summarisation
and assessment.

User Evaluation and Deployment

Live classroom testing, A/B comparisons with traditional materials, and structured feedback
collection could validate the impact on student understanding, engagement, and satisfaction.
Usability studies would also help improve interface design and workflow clarity.


Multimodal Input Support

While the current pipeline is optimized for text-based documents, future extensions could in-
clude slide deck ingestion, image captioning, and video transcription. This would allow broader
support for real-world teaching materials.

LLM Cost and Fine-Tuning Strategy

Cost remains a constraint. Exploring smaller or open-source models for specific subtasks (e.g.,
quiz generation) or fine-tuning domain-specific versions could reduce operational costs while
preserving quality.

### 10.6 Final Reflections

This project represented a complex but rewarding integration of NLP, educational theory, and
cloud infrastructure. Working at the intersection of these domains required balancing multiple
priorities, accuracy, pedagogy, scalability, and evaluation, each with its trade-offs. The most
demanding technical challenge was learning to navigate AWS services and deploying a fully
functional backend under tight project constraints. Amplify’s abstraction helped, but under-
standing its conventions and translating documentation into production-ready workflows was a
steep learning curve. The project also offered more profound insight into responsible AI in edu-
cation. Designing prompt structures to minimize hallucination, validating outputs with human-
aligned evaluators, and considering learner safety all highlighted the importance of transparency,
control, and ethical grounding. This project was initially intended to be one of four integrated
components forming a larger system that included goal decomposition, adaptive scheduling, and
focus monitoring. While full integration was not pursued due to time constraints, this module
was developed with a modular design and cloud-based architecture, making it suitable for future
integration into such a system. The interfaces, processing pipeline, and infrastructure reflect
collaborative engineering practices aligned with the platform’s original vision. Ultimately, this
work demonstrates that with the right structure, language models can support, not replace,
educators by transforming raw content into structured learning materials. While further val-
idation is needed, the current system lays the foundation for future classroom-ready scalable,
transparent, and pedagogically aligned tools.


