import { Network, Lock, Users, Zap, MessageSquare, Settings } from 'lucide-react';
import { ProjectDetailPage } from '../../components';
import type { ProjectDetail } from '../../components/ProjectDetailPage';

const netCommHubProject: ProjectDetail = {
  title: "NetCommHub - Group Communication System",
  image: {
    src: "https://images.unsplash.com/photo-1544197150-b99a580bb7a8?auto=format&fit=crop&w=1600&h=600",
    alt: "NetCommHub System"
  },
  tags: ["Java", "Networking", "Distributed Systems", "GUI"],
  description: "A robust networked distributed system enabling group-based client-server communication with a graphical user interface. The system implements a coordinator-based architecture with fault tolerance and real-time messaging capabilities.",
  features: [
    {
      icon: <Network className="w-6 h-6" />,
      title: "Distributed Architecture",
      description: "Robust client-server communication with coordinator-based design"
    },
    {
      icon: <Lock className="w-6 h-6" />,
      title: "Fault Tolerance",
      description: "Automatic coordinator reassignment and error handling"
    },
    {
      icon: <Users className="w-6 h-6" />,
      title: "Group Management",
      description: "Dynamic member discovery and role-based privileges"
    },
    {
      icon: <MessageSquare className="w-6 h-6" />,
      title: "Real-time Messaging",
      description: "Direct and broadcast messaging capabilities"
    },
    {
      icon: <Settings className="w-6 h-6" />,
      title: "Intuitive Interface",
      description: "Tab-based GUI with real-time updates"
    },
    {
      icon: <Zap className="w-6 h-6" />,
      title: "High Performance",
      description: "Efficient message routing and delivery"
    }
  ],
  technicalDetails: [
    "Java-based implementation with Swing GUI",
    "TCP/IP networking for reliable communication",
    "Thread-safe message handling",
    "Event-driven architecture",
    "Comprehensive error handling",
    "Efficient member discovery protocol",
    "Modular and extensible design"
  ],
  results: [
    "Successfully tested with multiple concurrent users",
    "Robust handling of network failures",
    "Minimal latency in message delivery",
    "Positive user feedback on interface design"
  ]
};

function NetCommHubPage() {
  return <ProjectDetailPage project={netCommHubProject} />;
}

export default NetCommHubPage;
