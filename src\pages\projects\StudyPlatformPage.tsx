import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>t, <PERSON><PERSON><PERSON>, <PERSON>, Zap } from 'lucide-react';
import { ProjectDetailPage } from '../../components';
import type { ProjectDetail } from '../../components/ProjectDetailPage';

const studyPlatformProject: ProjectDetail = {
  title: "AI-Powered Study Content Generator",
  image: {
    src: "https://res.cloudinary.com/dramsks0e/image/upload/v1750755613/AI-driven-educational-platform_ufzllw.png",
    alt: "Study Platform"
  },
  tags: ["React", "TypeScript", "AWS Amplify", "AI/ML"],
  description: "An advanced educational platform that leverages AI to transform lecture materials into summarized content and interactive quizzes. The system processes various document formats, generates concise summaries, and creates assessment materials automatically.",
  features: [
    {
      icon: <Brain className="w-6 h-6" />,
      title: "AI Processing",
      description: "Intelligent document analysis and content generation"
    },
    {
      icon: <FileText className="w-6 h-6" />,
      title: "Smart Summaries",
      description: "Automated generation of concise educational content"
    },
    {
      icon: <PenTool className="w-6 h-6" />,
      title: "Quiz Generation",
      description: "AI-powered creation of assessment materials"
    },
    {
      icon: <Users className="w-6 h-6" />,
      title: "User Management",
      description: "Role-based access for students and administrators"
    },
    {
      icon: <BookOpen className="w-6 h-6" />,
      title: "Content Review",
      description: "Administrative workflow for content approval"
    },
    {
      icon: <Zap className="w-6 h-6" />,
      title: "Real-time Updates",
      description: "Instant content delivery and progress tracking"
    }
  ],
  technicalDetails: [
    "React and TypeScript frontend with Vite",
    "AWS Amplify Gen 2 backend infrastructure",
    "Google Gemini AI for content processing",
    "Serverless architecture with AWS Lambda",
    "DynamoDB for data storage",
    "S3 for document storage",
    "Cognito for authentication"
  ],
  results: [
    "Reduced content creation time by 70%",
    "High accuracy in content summarization",
    "Positive feedback from educators and students",
    "Scalable infrastructure handling thousands of users"
  ]
};

function StudyPlatformPage() {
  return <ProjectDetailPage project={studyPlatformProject} />;
}

export default StudyPlatformPage;
