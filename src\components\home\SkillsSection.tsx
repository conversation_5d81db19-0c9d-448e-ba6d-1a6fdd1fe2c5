import React from 'react';
import { motion } from 'framer-motion';
import { Code2, Database, Layout, Server, Braces, Globe } from 'lucide-react';

interface SkillCardProps {
  icon: React.ReactNode;
  title: string;
  skills: string[];
}

interface SkillCategory {
  icon: React.ReactNode;
  title: string;
  skills: string[];
}

const skillCategories: SkillCategory[] = [
  {
    icon: <Code2 className="w-8 h-8" />,
    title: 'Frontend Development',
    skills: ['React', 'TypeScript', 'Next.js', 'Tailwind CSS']
  },
  {
    icon: <Server className="w-8 h-8" />,
    title: 'Backend Development',
    skills: ['Node.js', 'Python', 'Go', 'RESTful APIs']
  },
  {
    icon: <Database className="w-8 h-8" />,
    title: 'Databases',
    skills: ['PostgreSQL', 'MongoDB', 'Redis', 'Elasticsearch']
  },
  {
    icon: <Layout className="w-8 h-8" />,
    title: 'DevOps',
    skills: ['Docker', 'Kubernetes', 'AWS', 'CI/CD']
  },
  {
    icon: <Braces className="w-8 h-8" />,
    title: 'Programming Languages',
    skills: ['JavaScript', 'TypeScript', 'Python', 'Go']
  },
  {
    icon: <Globe className="w-8 h-8" />,
    title: 'Other Skills',
    skills: ['System Design', 'Agile', 'Git', 'Testing']
  }
];

const SkillCard: React.FC<SkillCardProps> = ({ icon, title, skills }) => {
  return (
    <motion.div
      className="bg-white dark:bg-gray-700 p-6 rounded-lg shadow-md dark:shadow-gray-900/20 transition-colors duration-300 h-full flex flex-col hover:shadow-xl hover:shadow-blue-500/10 dark:hover:shadow-purple-500/10 border border-gray-100 dark:border-gray-600"
      whileHover={{ y: -5, transition: { duration: 0.2 } }}
    >
      <div className="flex items-center mb-4">
        <div 
          className="p-3 rounded-lg mr-3 shadow-lg"
          style={{
            background: 'linear-gradient(135deg, rgb(59 130 246), rgb(147 51 234))'
          }}
        >
          <div className="text-white">
            {icon}
          </div>
        </div>
        <h3 className="text-xl font-semibold bg-gradient-to-r from-blue-600 to-purple-600 dark:from-blue-400 dark:to-purple-400 bg-clip-text text-transparent dark:drop-shadow-[0_1px_1px_rgba(0,0,0,0.8)]">{title}</h3>
      </div>
      <ul className="space-y-2 flex-grow">
        {skills.map((skill) => (
          <li key={skill} className="text-gray-600 dark:text-gray-300">{skill}</li>
        ))}
      </ul>
    </motion.div>
  );
};

const SkillsSection: React.FC = () => {
  const sectionVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.6 },
    },
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.3,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.5 },
    },
  };

  return (
    <motion.section
      variants={sectionVariants}
      initial="hidden"
      whileInView="visible"
      viewport={{ once: true, margin: "-100px" }}
      className="py-16 bg-gradient-to-b from-blue-50/20 via-gray-50 to-purple-50/25 dark:from-gray-800/80 dark:via-gray-800 dark:to-gray-800/90 transition-colors duration-300"
    >
      <div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8">
        <h2 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-12 text-center">Technical Skills</h2>
        <motion.div
          variants={containerVariants}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 items-stretch"
        >
          {skillCategories.map((category, index) => (
            <motion.div key={index} variants={itemVariants}>
              <SkillCard
                icon={category.icon}
                title={category.title}
                skills={category.skills}
              />
            </motion.div>
          ))}
        </motion.div>
      </div>
    </motion.section>
  );
};

export default SkillsSection;
